import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// GET: Fetch user's workflow auth configurations
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { data: authConfigs, error } = await supabase
      .from('workflow_auth_configs')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch auth configurations' },
        { status: 500 }
      )
    }

    // Remove sensitive data from response
    const sanitizedConfigs = authConfigs.map(config => ({
      ...config,
      auth_data: {
        ...config.auth_data,
        access_token: config.auth_data.access_token ? '[REDACTED]' : undefined,
        refresh_token: config.auth_data.refresh_token ? '[REDACTED]' : undefined,
        api_key: config.auth_data.api_key ? '[REDACTED]' : undefined,
        api_secret: config.auth_data.api_secret ? '[REDACTED]' : undefined
      }
    }))

    return NextResponse.json({ authConfigs: sanitizedConfigs })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST: Create new auth configuration (for API keys)
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      workflowType, 
      provider, 
      authMethod, 
      authData 
    } = body

    // Validate required fields
    if (!workflowType || !provider || !authMethod || !authData) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate auth data based on method
    if (authMethod === 'api_key' && !authData.api_key) {
      return NextResponse.json(
        { error: 'API key is required' },
        { status: 400 }
      )
    }

    // Test the API key/connection before storing
    let connectionStatus = 'connected'
    try {
      await testConnection(provider, authMethod, authData)
    } catch (error) {
      console.error('Connection test failed:', error)
      connectionStatus = 'error'
    }

    // Store auth configuration
    const authConfig = {
      user_id: user.id,
      workflow_type: workflowType,
      provider,
      auth_method: authMethod,
      auth_data: authData,
      status: connectionStatus,
      expires_at: null // API keys typically don't expire
    }

    const { data, error } = await supabase
      .from('workflow_auth_configs')
      .upsert(authConfig, {
        onConflict: 'user_id,workflow_type,provider'
      })
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to save authentication' },
        { status: 500 }
      )
    }

    // Return success without sensitive data
    return NextResponse.json({ 
      success: true,
      config: {
        ...data,
        auth_data: {
          ...data.auth_data,
          api_key: '[REDACTED]',
          api_secret: data.auth_data.api_secret ? '[REDACTED]' : undefined
        }
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE: Remove auth configuration
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const configId = searchParams.get('id')

    if (!configId) {
      return NextResponse.json(
        { error: 'Configuration ID is required' },
        { status: 400 }
      )
    }

    // Delete the configuration (with user ownership check)
    const { error } = await supabase
      .from('workflow_auth_configs')
      .delete()
      .eq('id', configId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to delete configuration' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Test connection for different providers
async function testConnection(provider: string, authMethod: string, authData: any) {
  switch (provider) {
    case 'sendgrid':
      return testSendGridConnection(authData.api_key)
    case 'twilio':
      return testTwilioConnection(authData.api_key, authData.api_secret)
    default:
      // For unknown providers, assume connection is valid
      return true
  }
}

async function testSendGridConnection(apiKey: string) {
  const response = await fetch('https://api.sendgrid.com/v3/user/profile', {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error('Invalid SendGrid API key')
  }

  return true
}

async function testTwilioConnection(accountSid: string, authToken: string) {
  const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}.json`, {
    headers: {
      'Authorization': `Basic ${Buffer.from(`${accountSid}:${authToken}`).toString('base64')}`
    }
  })

  if (!response.ok) {
    throw new Error('Invalid Twilio credentials')
  }

  return true
}