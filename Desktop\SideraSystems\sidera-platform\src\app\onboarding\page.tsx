"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { WorkflowAuth } from "@/components/workflow-auth"
import { 
  CheckCircle, 
  ArrowRight,
  Star,
  MessageSquare,
  Share2,
  Clock,
  Settings,
  Sparkles
} from "lucide-react"

const automationOptions = [
  {
    id: 'review_booster',
    name: 'Review Booster',
    description: 'Automatically request reviews from satisfied customers',
    icon: <Star className="h-6 w-6" />,
    color: 'text-green-400',
    popular: true,
    scheduleOptions: [
      { label: 'After each job completion', value: '0 9 * * *' },
      { label: 'Weekly on Mondays', value: '0 9 * * 1' },
      { label: 'Bi-weekly', value: '0 9 */14 * *' }
    ]
  },
  {
    id: 'seo_responder',
    name: '<PERSON><PERSON>sponder',
    description: 'AI-powered responses to Google Business reviews',
    icon: <MessageSquare className="h-6 w-6" />,
    color: 'text-blue-400',
    popular: false,
    scheduleOptions: [
      { label: 'Every 4 hours', value: '0 */4 * * *' },
      { label: 'Twice daily', value: '0 9,17 * * *' },
      { label: 'Daily at 9 AM', value: '0 9 * * *' }
    ]
  },
  {
    id: 'social_poster',
    name: 'Social Poster',
    description: 'Automated educational content posting',
    icon: <Share2 className="h-6 w-6" />,
    color: 'text-purple-400',
    popular: false,
    scheduleOptions: [
      { label: 'Weekly on Monday', value: '0 10 * * 1' },
      { label: 'Bi-weekly', value: '0 10 */14 * *' },
      { label: 'Monthly', value: '0 10 1 * *' }
    ]
  }
]

export default function Onboarding() {
  const [step, setStep] = useState(1)
  const [selectedAutomations, setSelectedAutomations] = useState<string[]>(['review_booster'])
  const [automationSettings, setAutomationSettings] = useState<Record<string, any>>({})
  const [authConfigs, setAuthConfigs] = useState([])

  const handleAutomationToggle = (automationId: string) => {
    setSelectedAutomations(prev => 
      prev.includes(automationId)
        ? prev.filter(id => id !== automationId)
        : [...prev, automationId]
    )
  }

  const handleScheduleChange = (automationId: string, schedule: string) => {
    setAutomationSettings(prev => ({
      ...prev,
      [automationId]: { ...prev[automationId], schedule }
    }))
  }

  const handleAuthConnect = async (provider: string, authMethod: string, data: any) => {
    console.log('Connect auth:', { provider, authMethod, data })
    // Here we would call our API to save the auth config
  }

  const handleAuthDisconnect = async (configId: string) => {
    console.log('Disconnect auth:', configId)
    // Here we would call our API to remove the auth config
  }

  const handleComplete = () => {
    console.log('Onboarding complete:', {
      selectedAutomations,
      automationSettings,
      authConfigs
    })
    // Save settings and redirect to dashboard
    window.location.href = '/dashboard'
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <Link href="/" className="flex items-center justify-center space-x-3 mb-8">
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                Proof Engine
              </div>
              <div className="text-sm text-slate-400 font-medium">
                by Sidera Systems
              </div>
            </div>
          </Link>
          
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-center space-x-2">
              <Sparkles className="h-5 w-5 text-amber-400" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-slate-100 to-slate-300 bg-clip-text text-transparent">
                Welcome aboard
              </h1>
              <Sparkles className="h-5 w-5 text-amber-400" />
            </div>
            
            <p className="text-lg text-slate-400 max-w-xl mx-auto leading-relaxed">
              Let's build your AI marketing engine. This setup takes just 3 minutes and will transform how you connect with customers.
            </p>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-6 mb-12">
          {[
            { step: 1, label: 'Choose Automations' },
            { step: 2, label: 'Configure Settings' },
            { step: 3, label: 'Connect Accounts' }
          ].map(({ step: stepNum, label }) => (
            <div key={stepNum} className="flex items-center">
              <div className={`relative w-10 h-10 rounded-xl flex items-center justify-center text-sm font-semibold transition-all duration-300 ${
                step >= stepNum 
                  ? 'bg-gradient-to-br from-emerald-500 to-emerald-600 text-white shadow-lg shadow-emerald-500/25 border border-emerald-400/50' 
                  : 'bg-slate-800/50 text-slate-400 border border-slate-700/50'
              }`}>
                {step > stepNum ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <span className="text-sm font-bold">{stepNum}</span>
                )}
                {step >= stepNum && (
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-emerald-400/20 to-transparent animate-pulse" />
                )}
              </div>
              <span className={`ml-3 text-sm font-medium transition-colors ${
                step >= stepNum ? 'text-slate-200' : 'text-slate-500'
              }`}>
                {label}
              </span>
              {stepNum < 3 && (
                <ArrowRight className={`h-4 w-4 mx-6 transition-colors ${
                  step >= stepNum ? 'text-slate-400' : 'text-slate-600'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step 1: Choose Automations */}
        {step === 1 && (
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                Choose Your Automations
              </h2>
              <p className="text-slate-400 text-lg max-w-2xl mx-auto leading-relaxed">
                Select the AI-powered automations that will transform your marketing. Each automation runs independently on your schedule.
              </p>
            </div>

            <div className="grid gap-6 max-w-4xl mx-auto">
              {automationOptions.map((automation) => (
                <Card 
                  key={automation.id}
                  className={`group relative overflow-hidden border-0 cursor-pointer transition-all duration-500 transform ${
                    selectedAutomations.includes(automation.id)
                      ? 'scale-[1.02] shadow-2xl shadow-emerald-500/20 bg-gradient-to-br from-slate-800/90 via-slate-800/80 to-emerald-900/20 ring-2 ring-emerald-500/50'
                      : 'hover:scale-[1.01] hover:shadow-xl hover:shadow-slate-900/50 bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 border border-slate-700/50'
                  }`}
                  onClick={() => handleAutomationToggle(automation.id)}
                >
                                    {/* Background overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-slate-900/20 via-transparent to-slate-900/40 pointer-events-none" />
                  
                  <CardHeader className="relative pb-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-5">
                        <div className={`relative p-4 rounded-2xl bg-gradient-to-br from-slate-700/80 to-slate-800/80 backdrop-blur border border-slate-600/50 shadow-lg ${automation.color}`}>
                          {automation.icon}
                          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />
                        </div>
                        <div className="space-y-2">
                          <div className="flex items-center gap-3">
                            <CardTitle className="text-xl font-bold text-slate-100">
                              {automation.name}
                            </CardTitle>
                            {automation.popular && (
                              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-lg">
                                Most Popular
                              </Badge>
                            )}
                          </div>
                          <CardDescription className="text-slate-400 text-base leading-relaxed">
                            {automation.description}
                          </CardDescription>
                        </div>
                      </div>
                      <div className={`relative w-8 h-8 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${
                        selectedAutomations.includes(automation.id)
                          ? 'border-emerald-400 bg-gradient-to-br from-emerald-500 to-emerald-600 shadow-lg shadow-emerald-500/25'
                          : 'border-slate-600 bg-slate-800/50 group-hover:border-slate-500'
                      }`}>
                        {selectedAutomations.includes(automation.id) && (
                          <CheckCircle className="h-5 w-5 text-white" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>

            <div className="text-center pt-4">
              <Button 
                onClick={() => setStep(2)}
                size="lg"
                disabled={selectedAutomations.length === 0}
                className="px-8 py-4 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-xl shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <span className="text-lg">
                  Continue with {selectedAutomations.length} automation{selectedAutomations.length !== 1 ? 's' : ''}
                </span>
                <ArrowRight className="ml-3 h-5 w-5" />
              </Button>
              
              {selectedAutomations.length === 0 && (
                <p className="text-slate-500 text-sm mt-3">
                  Select at least one automation to continue
                </p>
              )}
            </div>
          </div>
        )}

        {/* Step 2: Configure Settings */}
        {step === 2 && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-semibold mb-2">Configure Automation Settings</h2>
              <p className="text-muted-foreground">
                Set up schedules and preferences for your selected automations.
              </p>
            </div>

            <div className="space-y-4 max-w-3xl mx-auto">
              {selectedAutomations.map((automationId) => {
                const automation = automationOptions.find(a => a.id === automationId)!
                return (
                  <Card key={automationId} className="border-0 bg-card/50 backdrop-blur">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-4">
                        <div className={`p-3 rounded-lg bg-secondary/50 ${automation.color}`}>
                          {automation.icon}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{automation.name}</CardTitle>
                          <CardDescription>Configure when this automation runs</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium mb-3 block">
                            <Clock className="h-4 w-4 inline mr-2" />
                            Schedule
                          </label>
                          <div className="grid gap-2">
                            {automation.scheduleOptions.map((option) => (
                              <label
                                key={option.value}
                                className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
                                  automationSettings[automationId]?.schedule === option.value
                                    ? 'border-brand-600 bg-brand-600/5'
                                    : 'border-border hover:border-brand-600/50'
                                }`}
                              >
                                <input
                                  type="radio"
                                  name={`schedule-${automationId}`}
                                  value={option.value}
                                  checked={automationSettings[automationId]?.schedule === option.value}
                                  onChange={() => handleScheduleChange(automationId, option.value)}
                                  className="sr-only"
                                />
                                <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                                  automationSettings[automationId]?.schedule === option.value
                                    ? 'border-brand-600 bg-brand-600'
                                    : 'border-muted'
                                }`}>
                                  {automationSettings[automationId]?.schedule === option.value && (
                                    <div className="w-full h-full rounded-full bg-white scale-50" />
                                  )}
                                </div>
                                <span className="text-sm">{option.label}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            <div className="text-center space-x-4">
              <Button variant="outline" onClick={() => setStep(1)}>
                Back
              </Button>
              <Button onClick={() => setStep(3)} size="lg">
                Continue to Account Setup
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        )}

        {/* Step 3: Connect Accounts */}
        {step === 3 && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-semibold mb-2">Connect Your Accounts</h2>
              <p className="text-muted-foreground">
                Connect the services and platforms your automations will use.
              </p>
            </div>

            <div className="max-w-3xl mx-auto">
              {selectedAutomations.map((automationId) => (
                <div key={automationId} className="mb-8">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    {automationOptions.find(a => a.id === automationId)?.icon}
                    {automationOptions.find(a => a.id === automationId)?.name}
                  </h3>
                  <WorkflowAuth
                    workflowType={automationId as any}
                    authConfigs={authConfigs}
                    onAuthConnect={handleAuthConnect}
                    onAuthDisconnect={handleAuthDisconnect}
                  />
                </div>
              ))}
            </div>

            <div className="text-center space-x-4">
              <Button variant="outline" onClick={() => setStep(2)}>
                Back
              </Button>
              <Button onClick={handleComplete} size="lg">
                Complete Setup
                <CheckCircle className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}