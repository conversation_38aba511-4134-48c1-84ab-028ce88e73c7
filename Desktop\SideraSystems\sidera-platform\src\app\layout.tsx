import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/navigation";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Sidera Systems - AI Marketing Automation for Home Services",
  description: "Automate your marketing with AI-powered review management, SEO responses, and social media posting. Built for HVAC, plumbing, roofing, and home service businesses.",
  keywords: ["marketing automation", "home services", "HVAC marketing", "plumber marketing", "review automation", "SEO", "AI"],
  authors: [{ name: "Sidera Systems" }],
  openGraph: {
    title: "Sidera Systems - AI Marketing Automation",
    description: "Automate your marketing with AI-powered workflows",
    url: "https://siderasystems.com",
    siteName: "Sidera Systems",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Sidera Systems",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sidera Systems - AI Marketing Automation",
    description: "Automate your marketing with AI-powered workflows",
    images: ["/og-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.variable} font-sans antialiased min-h-screen bg-background`}>
        <Navigation />
        <main className="flex-1">
          {children}
        </main>
        <footer className="border-t border-slate-800/50 bg-slate-950/80 backdrop-blur">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                  Proof Engine
                </h3>
                <p className="text-sm text-slate-400 max-w-xs leading-relaxed">
                  AI-powered marketing automation that works 24/7 for home service businesses.
                </p>
              </div>
              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-slate-300">Product</h4>
                <ul className="space-y-2 text-sm text-slate-400">
                  <li><a href="/features" className="hover:text-white transition-colors">Features</a></li>
                  <li><a href="/pricing" className="hover:text-white transition-colors">Pricing</a></li>
                  <li><a href="/integrations" className="hover:text-white transition-colors">Integrations</a></li>
                </ul>
              </div>
              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-slate-300">Resources</h4>
                <ul className="space-y-2 text-sm text-slate-400">
                  <li><a href="/blog" className="hover:text-white transition-colors">Blog</a></li>
                  <li><a href="/docs" className="hover:text-white transition-colors">Documentation</a></li>
                  <li><a href="/support" className="hover:text-white transition-colors">Support</a></li>
                </ul>
              </div>
              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-slate-300">Company</h4>
                <ul className="space-y-2 text-sm text-slate-400">
                  <li><a href="/about" className="hover:text-white transition-colors">About</a></li>
                  <li><a href="/privacy" className="hover:text-white transition-colors">Privacy</a></li>
                  <li><a href="/terms" className="hover:text-white transition-colors">Terms</a></li>
                </ul>
              </div>
            </div>
            <div className="mt-16 pt-8 border-t border-slate-800/50 text-center text-sm text-slate-400">
              <p>&copy; 2024 Sidera Systems. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}
