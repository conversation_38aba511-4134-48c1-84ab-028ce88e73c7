# Sidera Systems - Proof Engine Architecture

## 🏗️ **Complete System Architecture & Deployment Strategy**

### **Overview**
Sidera Systems "Proof Engine" is a **Full SaaS Platform for AI Marketing Automations** targeting home service businesses. We provide **only TWO core workflows** that scale using the proven agency n8n pattern.

---

## 🎯 **Supported Workflows (MVP)**

### **1. SEO Responder**
- **Purpose**: AI-powered responses to Google Business reviews with SEO optimization
- **Client Auth**: Client provides Google Business API Key
- **Automation**: Monitors reviews → Generates AI responses → Posts back to Google Business
- **Benefits**: Improves local SEO rankings, customer engagement, review response rate

### **2. Social Poster** 
- **Purpose**: Automated posting to Facebook business pages
- **Client Auth**: OAuth to OUR Facebook app (clients just connect their page)
- **Automation**: Generates educational content → Posts to Facebook Business Page
- **Benefits**: Consistent social presence, educational content, seasonal tips

---

## 🚀 **Deployment Strategy (3 Phases)**

### **Phase 1: Development (Current)**
```bash
# Local Development Setup
Terminal 1: npx n8n                    # http://localhost:5678
Terminal 2: npm run dev                # http://localhost:3000

# n8n connects to Next.js via localhost API calls
```

### **Phase 2: MVP Launch (Vercel + zrok)**
```bash
# Production Setup
Vercel: Next.js Platform              # yourdomain.com
Local: n8n via zrok tunnel            # abcd1234.share.zrok.io

# Vercel connects to n8n via zrok secure tunnel
```

### **Phase 3: Scale (Vercel + Railway)**
```bash
# Scaled Production
Vercel: Next.js Platform              # yourdomain.com  
Railway: n8n Instance                 # workflows.yourdomain.com

# Vercel connects to Railway n8n via HTTPS
```

---

## 🔧 **Technical Stack**

### **Frontend (Next.js on Vercel)**
- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS + shadcn/ui (dark matte theme)
- **Authentication**: Supabase Auth
- **Payments**: Stripe integration
- **Deployment**: Vercel (Fluid Compute enabled)

### **Backend Database (Supabase)**
- **Auth**: User authentication and session management
- **Tables**: 
  - `profiles` - User account data
  - `client_configs` - User automation settings
  - `automation_configs` - Workflow configurations
  - `workflow_auth_configs` - Provider authentication (OAuth tokens, API keys)
  - `logs` - Workflow execution logs
  - `posts` - Generated content storage

### **Workflow Engine (n8n)**
- **Purpose**: Execute per-user automation workflows
- **Control**: Programmatic via REST API
- **Templates**: Pre-built workflow templates for each automation type
- **Scaling**: One workflow instance per user per automation type
- **Scheduling**: Per-user cron expressions

### **External Integrations**
- **Google Business API**: Review monitoring and response posting
- **Facebook Graph API**: Social media posting
- **Google Gemini AI**: Content generation and SEO-optimized responses
- **Stripe API**: Subscription billing and webhooks

---

## 🏛️ **System Architecture Diagram**

```
┌─────────────────────────────────────────────────────────────────┐
│                    CLIENT INTERFACE                             │
├─────────────────────────────────────────────────────────────────┤
│  Marketing Site  │  Client Dashboard  │  Admin Panel            │
│  (Public)        │  (Authenticated)   │  (Internal)             │
└─────────────────────────────────────────────────────────────────┘
                                │
                    ┌───────────▼──────────┐
                    │   Next.js API Routes  │
                    │   (Vercel)           │
                    └───────────┬──────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌───────▼──────┐    ┌──────────▼─────────┐    ┌────────▼──────┐
│   Supabase   │    │  n8n Workflow      │    │   Stripe      │
│   Database   │    │  Engine            │    │   Billing     │
│              │    │                    │    │               │
│ • profiles   │    │ • SEO Responder    │    │ • Checkout    │
│ • configs    │    │ • Social Poster    │    │ • Webhooks    │
│ • auth_data  │    │ • Per-user crons   │    │ • Billing     │
│ • logs       │    │ • REST API         │    │               │
└──────────────┘    └────────┬───────────┘    └───────────────┘
                              │
                    ┌─────────▼─────────┐
                    │  External APIs     │
                    │                   │
                    │ • Google Business │
                    │ • Facebook Graph  │
                    │ • Gemini AI       │
                    └───────────────────┘
```

---

## 🔄 **Client User Flow**

### **1. Sign Up & Onboarding**
```mermaid
graph TD
    A[Visit yourdomain.com] --> B[Sign Up]
    B --> C[Choose Plan & Pay]
    C --> D[Onboarding Flow]
    D --> E[Select Workflows]
    E --> F{Workflow Type}
    
    F -->|SEO Responder| G[Enter Google Business API Key]
    F -->|Social Poster| H[OAuth to Our Facebook App]
    
    G --> I[Configure Schedule]
    H --> I
    I --> J[Workflows Activated]
    J --> K[Monitor Dashboard]
```

### **2. Dashboard Experience**
- **Overview**: Performance metrics, recent activity
- **Automations**: Toggle on/off, modify schedules, view status
- **Analytics**: Response rates, engagement metrics, ROI tracking
- **Settings**: Update authentication, modify configurations
- **Billing**: Plan management, usage tracking

### **3. Automation Management**
- **SEO Responder**: View/edit AI response templates, see recent responses
- **Social Poster**: Content calendar, posting schedule, engagement metrics

---

## 👩‍💼 **Admin User Flow**

### **1. System Management**
- **User Overview**: All users, their plans, usage statistics
- **Workflow Monitoring**: n8n dashboard access, execution logs
- **Template Management**: Update workflow templates via n8n GUI
- **System Health**: Monitor API usage, error rates, performance

### **2. Business Operations**
- **Billing Management**: Stripe dashboard, failed payments, plan changes
- **Customer Support**: User lookup, workflow debugging, configuration help
- **Analytics**: Platform-wide metrics, revenue tracking, growth analysis

### **3. Technical Operations**
- **n8n Management**: Direct access to workflow builder
- **API Monitoring**: External API usage, rate limits, costs
- **Error Handling**: Failed workflow executions, integration issues

---

## ⚙️ **Workflow Execution Deep Dive**

### **Template System (Agency Pattern)**
```typescript
// Each automation type has a template
const WORKFLOW_TEMPLATES = {
  seo_responder: {
    name: 'SEO Responder',
    nodes: [
      scheduleTrigger,      // Per-user cron
      getUserConfig,        // Pull from Supabase  
      checkReviews,         // Google Business API
      generateResponse,     // Gemini AI
      postResponse,         // Back to Google Business
      logResult            // Store in Supabase
    ]
  },
  social_poster: { /* Similar structure */ }
}
```

### **Per-User Workflow Creation**
```typescript
// When client activates automation:
const userWorkflow = await n8nApi.createUserWorkflow(
  userId: "user-123",
  automationType: "seo_responder", 
  cronExpression: "0 9 * * *",     // 9 AM daily
  userSettings: {
    googleBusinessApiKey: "client-key",
    locationId: "client-location"
  }
)
```

### **Execution Flow**
1. **Cron Trigger**: Fires based on user's schedule
2. **Config Loading**: Retrieves user-specific settings from Supabase
3. **Authentication**: Uses stored OAuth tokens or API keys
4. **External API Calls**: Executes workflow-specific logic
5. **AI Processing**: Generates content using Gemini API
6. **Result Processing**: Posts back to target platform
7. **Logging**: Stores execution results for user dashboard

---

## 🔐 **Authentication Architecture**

### **Workflow-Specific Auth (Key Innovation)**
We handle authentication differently per workflow to optimize user experience:

#### **SEO Responder (API Key)**
- **What**: Client provides Google Business API Key
- **Why**: Simple, no OAuth complexity
- **Storage**: Encrypted in `workflow_auth_configs` table
- **Usage**: Direct API calls with client's key

#### **Social Poster (OAuth)**
- **What**: OAuth to OUR Facebook app
- **Why**: We manage one app, clients just connect pages
- **Storage**: Access tokens in `workflow_auth_configs` table  
- **Usage**: Clients grant page permissions to our app

### **Security Model**
- **Encryption**: All auth data encrypted at rest
- **Isolation**: User tokens never shared between clients
- **Audit Trail**: All API calls logged with user context
- **Expiration**: Automatic token refresh where supported

---

## 🌐 **Deployment Configurations**

### **Phase 2: MVP with zrok**

#### **Environment Variables (.env.local)**
```bash
# Vercel Production
NEXT_PUBLIC_APP_URL=https://yourdomain.com
N8N_API_URL=https://abcd1234.share.zrok.io  # zrok tunnel
N8N_API_KEY=your_n8n_api_key

# Still use Supabase, Stripe, etc.
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
STRIPE_SECRET_KEY=your_stripe_key
```

#### **Local n8n with zrok**
```bash
# Terminal 1: Start n8n
npx n8n

# Terminal 2: Create zrok tunnel  
zrok share public http://localhost:5678
# Output: https://abcd1234.share.zrok.io
```

#### **Benefits**
- ✅ **Zero server costs** initially
- ✅ **Full n8n functionality** (dashboard, GUI)
- ✅ **Easy debugging** (local logs)
- ✅ **Quick iteration** on workflows
- ✅ **Secure tunnel** (better than ngrok for production)

### **Phase 3: Scaled with Railway**

#### **Railway Deployment**
```dockerfile
# Dockerfile for Railway
FROM n8nio/n8n:latest

# Set production environment
ENV N8N_HOST=workflows.yourdomain.com
ENV N8N_PROTOCOL=https
ENV N8N_PORT=5678

EXPOSE 5678
CMD ["n8n", "start"]
```

#### **Environment Variables (Railway)**
```bash
N8N_HOST=workflows.yourdomain.com
N8N_PROTOCOL=https
DATABASE_TYPE=postgres
DB_POSTGRESDB_HOST=railway_postgres_host
N8N_API_KEY=production_api_key
```

#### **Benefits**
- ✅ **High availability** (managed infrastructure)
- ✅ **Auto-scaling** based on usage
- ✅ **Professional setup** for enterprise clients
- ✅ **Backup & monitoring** included
- ✅ **Custom domain** support

---

## 💰 **Cost Analysis**

### **Phase 2 Costs (MVP)**
- **Vercel**: Free tier or $20/month (Pro)
- **Supabase**: Free tier or $25/month (Pro)  
- **zrok**: Free (community)
- **Stripe**: 2.9% + 30¢ per transaction
- **Total**: ~$0-45/month + transaction fees

### **Phase 3 Costs (Scale)**
- **Vercel**: $20/month (Pro) or $40/month (Team)
- **Railway**: $5-20/month (n8n instance)
- **Supabase**: $25/month (Pro)
- **Domain**: $10/year
- **Total**: ~$50-85/month + usage

### **Revenue Model**
- **Starter**: $49/month (1 workflow)
- **Professional**: $99/month (both workflows)  
- **Enterprise**: $199/month (custom features)

---

## 🔍 **Monitoring & Observability**

### **Application Monitoring**
- **Vercel**: Built-in function logs, performance metrics
- **Supabase**: Database performance, query analytics
- **Custom**: User engagement tracking, conversion funnels

### **Workflow Monitoring**
- **n8n Dashboard**: Execution logs, error tracking, performance
- **API Monitoring**: External API usage, rate limits, failures
- **User Impact**: Success rates, automation effectiveness

### **Business Metrics**
- **Growth**: Sign-ups, conversions, churn rate
- **Usage**: Workflow executions, API calls, feature adoption
- **Revenue**: MRR, ARPU, customer lifetime value

---

## 🚀 **Scaling Strategy**

### **User Growth Triggers**
- **0-10 users**: Phase 2 (zrok tunnel)
- **10-100 users**: Phase 3 (Railway)
- **100+ users**: Consider dedicated infrastructure

### **Performance Scaling**
- **Database**: Supabase auto-scales
- **Workflows**: n8n horizontal scaling on Railway
- **Frontend**: Vercel Edge Network handles traffic

### **Feature Scaling**
- **Additional Workflows**: Easy to add new templates
- **Enterprise Features**: White-labeling, custom integrations
- **API Access**: Public API for advanced users

---

## 🛠️ **Development Workflow**

### **Local Development**
```bash
# Start full development environment
Terminal 1: npx n8n                    # Workflow engine
Terminal 2: npm run dev                # Next.js platform

# Environment: .env.local with localhost URLs
```

### **Template Development**
1. **Design**: Build workflows in n8n GUI
2. **Test**: Use sample data to verify functionality  
3. **Export**: Extract workflow JSON for templates
4. **Deploy**: Update `WORKFLOW_TEMPLATES` in code

### **Feature Development**
1. **Frontend**: Build UI components with shadcn/ui
2. **Backend**: Create API routes for new functionality
3. **Integration**: Connect to n8n via REST API
4. **Testing**: End-to-end workflow testing

---

## 🔮 **Future Enhancements**

### **Additional Workflows**
- **Email Marketing**: Automated email sequences
- **Lead Management**: CRM integration and follow-ups
- **Reputation Management**: Multi-platform review monitoring

### **Advanced Features**
- **AI Training**: Custom models based on user data
- **Advanced Analytics**: Predictive insights, recommendations
- **White Labeling**: Custom branding for agencies

### **Enterprise Features**
- **Multi-location**: Support for franchise businesses
- **Team Management**: Multiple users per account
- **Custom Integrations**: Bespoke workflow development

---

## 📚 **Technical Documentation**

### **API Integration**
- **n8n REST API**: Full documentation for workflow management
- **Supabase SDK**: Database operations and real-time subscriptions
- **Stripe API**: Payment processing and webhook handling

### **Workflow Development**
- **Template Structure**: How to create new workflow templates
- **User Configuration**: Managing per-user settings and authentication
- **Error Handling**: Robust error handling and recovery

### **Deployment Guide**
- **Environment Setup**: Complete environment variable reference
- **Security Checklist**: Production security considerations
- **Monitoring Setup**: Logging and alerting configuration

---

## 🎯 **Success Metrics**

### **Technical KPIs**
- **Uptime**: >99.9% availability
- **Performance**: <2s page load times
- **Reliability**: >95% workflow success rate

### **Business KPIs**
- **User Growth**: 20% MoM growth rate
- **Retention**: >90% monthly retention
- **Revenue**: $10k ARR by month 12

### **User Experience KPIs**
- **Onboarding**: <5 minutes to first automation
- **Support**: <24h response time
- **Satisfaction**: >4.5/5 user rating

---

This architecture provides a **scalable, cost-effective path** from MVP to enterprise SaaS platform, leveraging proven patterns and modern infrastructure! 🚀