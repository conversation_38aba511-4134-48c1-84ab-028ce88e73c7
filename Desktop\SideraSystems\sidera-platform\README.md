# Sidera Systems - Proof Engine

**AI Marketing Automation Platform for Home Service Businesses**

Built with Next.js, Supabase, n8n, Stripe, and Gemini AI according to the comprehensive specifications in `myproject.md`.

## 🏗️ Architecture Overview

### Core Stack
- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Backend Database**: Supabase (PostgreSQL) with real-time subscriptions
- **Workflow Engine**: n8n (self-hosted) with REST API control
- **Payments**: Stripe with webhooks for subscription management
- **AI Content**: Google Gemini 2.5 Pro for blog generation and SEO responses
- **Authentication**: Workflow-specific OAuth and API key management

### Key Features
- **Per-user cron control** of n8n workflows via REST API
- **Dark matte UI** with polished cards and professional design
- **OAuth integration** for Facebook, Google Business Profile
- **API key management** for SendGrid, Twilio, and other services
- **Real-time analytics** and workflow execution logging
- **Admin dashboard** for user and workflow management

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env.local

# Install dependencies
npm install

# Start development server
npm run dev
```

### 2. Required Environment Variables

```env
# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000
ADMIN_EMAIL=<EMAIL>

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# n8n (Self-hosted)
N8N_API_URL=http://localhost:5678
N8N_API_KEY=your_n8n_api_key

# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_BASIC_PRICE_ID=price_basic_plan_id
STRIPE_STANDARD_PRICE_ID=price_standard_plan_id
STRIPE_PREMIUM_PRICE_ID=price_premium_plan_id

# OAuth Providers
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# AI
GOOGLE_AI_API_KEY=your_gemini_api_key

# Email/SMS Services
SENDGRID_API_KEY=SG.your_sendgrid_api_key
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
```

### 3. Database Setup (Supabase)

Create the following tables in your Supabase project:

```sql
-- Profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  business_name TEXT,
  business_category TEXT,
  business_location TEXT,
  subscription_tier TEXT CHECK (subscription_tier IN ('basic', 'standard', 'premium')),
  subscription_status TEXT CHECK (subscription_status IN ('active', 'cancelled', 'past_due')),
  stripe_customer_id TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client configurations
CREATE TABLE client_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  google_business_api_key TEXT,
  google_business_location_id TEXT,
  preferred_tone TEXT CHECK (preferred_tone IN ('friendly', 'professional', 'casual')),
  contact_phone TEXT,
  contact_email TEXT,
  business_hours JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow authentication configurations
CREATE TABLE workflow_auth_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  workflow_type TEXT CHECK (workflow_type IN ('review_booster', 'seo_responder', 'social_poster')),
  provider TEXT NOT NULL,
  auth_method TEXT CHECK (auth_method IN ('oauth', 'api_key', 'webhook', 'email_password')),
  auth_data JSONB NOT NULL,
  status TEXT CHECK (status IN ('connected', 'expired', 'error', 'pending')) DEFAULT 'pending',
  expires_at TIMESTAMP WITH TIME ZONE,
  provider_user_id TEXT,
  provider_username TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, workflow_type, provider)
);

-- Automation configurations
CREATE TABLE automation_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  automation_type TEXT CHECK (automation_type IN ('review_booster', 'seo_responder', 'social_poster')),
  is_active BOOLEAN DEFAULT false,
  cron_schedule TEXT NOT NULL,
  workflow_id TEXT,
  settings JSONB,
  last_run TIMESTAMP WITH TIME ZONE,
  next_run TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, automation_type)
);

-- Execution logs
CREATE TABLE logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  automation_type TEXT CHECK (automation_type IN ('review_booster', 'seo_responder', 'social_poster')),
  event_type TEXT CHECK (event_type IN ('success', 'error', 'warning', 'info')),
  message TEXT NOT NULL,
  details JSONB,
  workflow_execution_id TEXT,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Blog posts
CREATE TABLE posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT,
  category TEXT,
  tags TEXT[],
  seo_keywords TEXT[],
  published BOOLEAN DEFAULT false,
  featured_image TEXT,
  author TEXT,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_automation_configs_user_id ON automation_configs(user_id);
CREATE INDEX idx_logs_user_id_timestamp ON logs(user_id, timestamp DESC);
CREATE INDEX idx_workflow_auth_configs_user_workflow ON workflow_auth_configs(user_id, workflow_type);
CREATE INDEX idx_posts_published_created ON posts(published, created_at DESC);
```

### 4. n8n Setup

1. **Install n8n** (self-hosted):
```bash
npm install -g n8n
# or
docker run -it --rm --name n8n -p 5678:5678 -v ~/.n8n:/home/<USER>/.n8n n8nio/n8n
```

2. **Enable API access** in n8n settings:
```json
{
  "endpoints": {
    "rest": "rest",
    "webhook": "webhook",
    "webhookTest": "webhook-test"
  },
  "api": {
    "enabled": true,
    "auth": {
      "type": "apiKey",
      "apiKey": "your_n8n_api_key"
    }
  }
}
```

## 📊 API Endpoints

### Authentication & User Management
- `POST /api/auth/callback/[provider]` - OAuth callback handler
- `GET/POST/DELETE /api/workflow-auth` - Manage workflow auth configs
- `GET/POST/PUT /api/automation-config` - Manage automation settings

### Core Workflow Control (myproject.md specification)
- `PATCH /api/update-cron` - Updates user cron → updates n8n workflow
- `GET /api/logs` - Returns workflow execution logs for dashboard
- `POST /api/webhooks/stripe` - Handles billing state updates → updates Supabase

### Content Generation
- `POST/GET /api/generate-blog` - AI blog post generation with Gemini

## 🔄 Workflow Authentication System

Each workflow supports different authentication methods as specified in myproject.md:

### OAuth Workflows (Facebook, Google Business)
```typescript
// Client clicks "Connect Facebook" → OAuth flow
const authUrl = `${provider.auth_url}?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scopes}`
// → Facebook approval → callback handler → store tokens
```

### API Key Workflows (SendGrid, Twilio)
```typescript
// Client enters API key → test connection → store encrypted
await testConnection(provider, 'api_key', { api_key: userKey })
```

### Webhook Workflows (Future)
```typescript
// Generate webhook URL → client configures → verify signature
const webhookUrl = `${appUrl}/api/webhooks/${workflowType}/${userId}`
```

## 🎛️ Per-User Cron Control

The system provides **individual cron control per user** via the n8n REST API:

```typescript
// User updates schedule → deactivate workflow → update cron → reactivate
await n8nApi.deactivateWorkflow(workflowId)
await n8nApi.updateWorkflowSchedule(workflowId, newCronExpression)
await n8nApi.activateWorkflow(workflowId)
```

**Supported Cron Patterns:**
- `0 9 * * *` - Daily at 9 AM
- `0 */4 * * *` - Every 4 hours
- `0 10 * * 1` - Weekly on Monday at 10 AM
- Custom expressions via cron-parser

## 🎨 UI Design System

### Dark Matte Theme
- **Background**: `#0a0a0a` (deep black)
- **Cards**: `#161616` (matte dark gray)
- **Borders**: `#2d2d2d` (subtle gray)
- **Primary**: `#ededed` (soft white)
- **Brand Gradient**: `#1e293b` → `#0f172a`

### Component Library
- **shadcn/ui** components with custom styling
- **Lucide React** icons throughout
- **Tailwind CSS** with custom theme
- **Polished cards** with subtle shadows and hover effects

## 🔧 Development

### Project Structure
```
sidera-platform/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (auth)/            # Auth pages (login, signup)
│   │   ├── dashboard/         # Client dashboard
│   │   ├── admin/            # Admin dashboard
│   │   ├── onboarding/       # Client onboarding flow
│   │   └── api/              # API routes
│   │       ├── auth/callback/[provider]/  # OAuth callbacks
│   │       ├── workflow-auth/             # Auth management
│   │       ├── automation-config/         # Automation settings
│   │       ├── update-cron/              # Cron updates (myproject.md)
│   │       ├── logs/                     # Execution logs (myproject.md)
│   │       ├── webhooks/stripe/          # Stripe webhooks (myproject.md)
│   │       └── generate-blog/            # AI blog generation
│   ├── components/            # React components
│   │   ├── ui/               # shadcn/ui components
│   │   ├── navigation.tsx    # Main navigation
│   │   └── workflow-auth.tsx # Workflow auth management
│   └── lib/                  # Utility libraries
│       ├── supabase/         # Database client & types
│       ├── types/auth.ts     # Auth type definitions
│       ├── stripe.ts         # Stripe integration
│       ├── n8n.ts           # n8n API wrapper
│       └── utils.ts         # Shared utilities
```

### Key Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

## 🚀 Deployment

### Environment Setup
1. **Supabase**: Create project, set up database tables
2. **n8n**: Deploy self-hosted instance with API enabled
3. **Stripe**: Configure products, prices, and webhooks
4. **OAuth Apps**: Create Facebook and Google apps
5. **AI Services**: Set up Gemini API access

### Production Considerations
- **Database**: Enable Row Level Security (RLS) in Supabase
- **API Keys**: Use proper secret management
- **n8n**: Deploy with persistent storage and backups
- **Monitoring**: Set up logging and error tracking
- **Rate Limiting**: Implement API rate limiting
- **Security**: Configure CORS, CSP headers

## 📝 License

Private commercial project - All rights reserved.

---

**Built according to myproject.md specifications**
- ✅ Next.js + Supabase + n8n + Stripe + Gemini AI stack
- ✅ Per-user cron control via n8n REST API
- ✅ Workflow-specific authentication (OAuth/API keys)
- ✅ Dark matte UI with polished design
- ✅ Complete admin and client dashboards
- ✅ AI blog generation and SEO automation
- ✅ Stripe subscription management with webhooks