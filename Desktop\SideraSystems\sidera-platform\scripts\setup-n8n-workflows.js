#!/usr/bin/env node

/**
 * Sidera Systems - n8n Workflow Setup Script
 * 
 * This script helps deploy workflow templates to n8n instance
 * Run with: node scripts/setup-n8n-workflows.js
 */

const axios = require('axios')
require('dotenv').config({ path: '.env.local' })

const N8N_API_URL = process.env.N8N_API_URL || 'http://localhost:5678'
const N8N_API_KEY = process.env.N8N_API_KEY || 'test'

// Create n8n API client
const n8nAPI = axios.create({
  baseURL: N8N_API_URL,
  headers: {
    'X-N8N-API-KEY': N8N_API_KEY,
    'Content-Type': 'application/json',
  },
})

// Workflow templates for Sidera Systems
const WORKFLOW_TEMPLATES = {
  seo_responder: {
    name: 'SEO Responder Template',
    active: false,
    nodes: [
      {
        id: 'schedule-trigger',
        type: 'n8n-nodes-base.scheduleTrigger',
        name: 'Schedule Trigger',
        parameters: {
          rule: {
            interval: [
              {
                field: 'cronExpression',
                value: '0 */4 * * *', // Every 4 hours
              },
            ],
          },
        },
        position: [250, 300],
        typeVersion: 1,
      },
      {
        id: 'get-user-config',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Get User Config',
        parameters: {
          url: '{{$env.NEXT_PUBLIC_APP_URL}}/api/client-config',
          method: 'GET',
          headers: {
            'authorization': 'Bearer {{$workflow.settings.apiKey}}',
          },
          qs: {
            user_id: '{{$workflow.settings.userId}}',
          },
        },
        position: [450, 300],
        typeVersion: 1,
      },
      {
        id: 'check-reviews',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Check Google Business Reviews',
        parameters: {
          url: 'https://mybusiness.googleapis.com/v4/accounts/{{$node["Get User Config"].json["google_business_location_id"]}}/locations/{{$node["Get User Config"].json["google_business_location_id"]}}/reviews',
          method: 'GET',
          headers: {
            'authorization': 'Bearer {{$node["Get User Config"].json["google_business_api_key"]}}',
          },
        },
        position: [650, 300],
        typeVersion: 1,
      },
      {
        id: 'filter-new-reviews',
        type: 'n8n-nodes-base.function',
        name: 'Filter New Reviews',
        parameters: {
          functionCode: `
            // Filter reviews that need responses
            const reviews = items[0].json.reviews || [];
            const lastProcessed = $workflow.staticData.lastProcessed || 0;
            
            const newReviews = reviews.filter(review => {
              const reviewTime = new Date(review.createTime).getTime();
              return reviewTime > lastProcessed && !review.reviewReply;
            });
            
            // Update last processed time
            $workflow.staticData.lastProcessed = Date.now();
            
            return newReviews.map(review => ({ json: review }));
          `,
        },
        position: [850, 300],
        typeVersion: 1,
      },
      {
        id: 'generate-ai-response',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Generate AI Response',
        parameters: {
          url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
          method: 'POST',
          headers: {
            'authorization': 'Bearer {{$env.GOOGLE_AI_API_KEY}}',
            'content-type': 'application/json',
          },
          body: {
            contents: [{
              parts: [{
                text: \`Generate a professional, SEO-optimized response to this Google Business review. 
                
                Review: "{{$json.comment}}"
                Rating: {{$json.starRating}} stars
                Business: {{$workflow.settings.businessName}}
                
                Response should be:
                - Professional and grateful
                - Include business name and relevant keywords
                - 50-150 words
                - Encourage future visits\`
              }]
            }]
          },
        },
        position: [1050, 300],
        typeVersion: 1,
      },
      {
        id: 'post-response',
        type: 'n8n-nodes-base.httpRequest', 
        name: 'Post Response to Google',
        parameters: {
          url: 'https://mybusiness.googleapis.com/v4/{{$json.name}}/reply',
          method: 'PUT',
          headers: {
            'authorization': 'Bearer {{$node["Get User Config"].json["google_business_api_key"]}}',
            'content-type': 'application/json',
          },
          body: {
            comment: '{{$node["Generate AI Response"].json.candidates[0].content.parts[0].text}}',
          },
        },
        position: [1250, 300],
        typeVersion: 1,
      },
      {
        id: 'log-result',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Log Execution',
        parameters: {
          url: '{{$env.NEXT_PUBLIC_APP_URL}}/api/logs',
          method: 'POST',
          headers: {
            'authorization': 'Bearer {{$workflow.settings.apiKey}}',
            'content-type': 'application/json',
          },
          body: {
            user_id: '{{$workflow.settings.userId}}',
            automation_type: 'seo_responder',
            event_type: 'success',
            message: 'AI response posted to Google Business review',
            details: JSON.stringify({
              reviewId: '{{$json.reviewId}}',
              response: '{{$node["Generate AI Response"].json.candidates[0].content.parts[0].text}}',
            }),
            workflow_execution_id: '{{$workflow.id}}',
          },
        },
        position: [1450, 300],
        typeVersion: 1,
      },
    ],
    connections: {
      'Schedule Trigger': {
        main: [
          [
            {
              node: 'Get User Config',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Get User Config': {
        main: [
          [
            {
              node: 'Check Google Business Reviews',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Check Google Business Reviews': {
        main: [
          [
            {
              node: 'Filter New Reviews',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Filter New Reviews': {
        main: [
          [
            {
              node: 'Generate AI Response',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Generate AI Response': {
        main: [
          [
            {
              node: 'Post Response to Google',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Post Response to Google': {
        main: [
          [
            {
              node: 'Log Execution',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
    },
    settings: {
      executionOrder: 'v1',
    },
  },
  
  social_poster: {
    name: 'Social Poster Template',
    active: false,
    nodes: [
      {
        id: 'schedule-trigger',
        type: 'n8n-nodes-base.scheduleTrigger',
        name: 'Schedule Trigger',
        parameters: {
          rule: {
            interval: [
              {
                field: 'cronExpression',
                value: '0 10 * * 1', // Weekly on Monday at 10 AM
              },
            ],
          },
        },
        position: [250, 300],
        typeVersion: 1,
      },
      {
        id: 'get-user-config',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Get User Config',
        parameters: {
          url: '{{$env.NEXT_PUBLIC_APP_URL}}/api/client-config',
          method: 'GET',
          headers: {
            'authorization': 'Bearer {{$workflow.settings.apiKey}}',
          },
          qs: {
            user_id: '{{$workflow.settings.userId}}',
          },
        },
        position: [450, 300],
        typeVersion: 1,
      },
      {
        id: 'generate-content',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Generate Educational Content',
        parameters: {
          url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
          method: 'POST',
          headers: {
            'authorization': 'Bearer {{$env.GOOGLE_AI_API_KEY}}',
            'content-type': 'application/json',
          },
          body: {
            contents: [{
              parts: [{
                text: \`Create an educational social media post for a {{$workflow.settings.businessCategory}} business.
                
                Business Name: {{$workflow.settings.businessName}}
                Service Area: {{$workflow.settings.serviceArea}}
                Current Season: {{new Date().getMonth() < 3 ? 'Winter' : new Date().getMonth() < 6 ? 'Spring' : new Date().getMonth() < 9 ? 'Summer' : 'Fall'}}
                
                Post should:
                - Provide helpful tips related to {{$workflow.settings.businessCategory}}
                - Be engaging and educational
                - Include seasonal relevance
                - Be 100-200 words
                - Include 2-3 relevant hashtags
                - Encourage engagement\`
              }]
            }]
          },
        },
        position: [650, 300],
        typeVersion: 1,
      },
      {
        id: 'post-to-facebook',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Post to Facebook Page',
        parameters: {
          url: 'https://graph.facebook.com/v18.0/{{$workflow.settings.facebookPageId}}/feed',
          method: 'POST',
          headers: {
            'content-type': 'application/json',
          },
          qs: {
            access_token: '{{$workflow.settings.facebookAccessToken}}',
            message: '{{$node["Generate Educational Content"].json.candidates[0].content.parts[0].text}}',
          },
        },
        position: [850, 300],
        typeVersion: 1,
      },
      {
        id: 'log-result',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Log Execution',
        parameters: {
          url: '{{$env.NEXT_PUBLIC_APP_URL}}/api/logs',
          method: 'POST',
          headers: {
            'authorization': 'Bearer {{$workflow.settings.apiKey}}',
            'content-type': 'application/json',
          },
          body: {
            user_id: '{{$workflow.settings.userId}}',
            automation_type: 'social_poster',
            event_type: 'success',
            message: 'Educational content posted to Facebook',
            details: JSON.stringify({
              postId: '{{$json.id}}',
              content: '{{$node["Generate Educational Content"].json.candidates[0].content.parts[0].text}}',
            }),
            workflow_execution_id: '{{$workflow.id}}',
          },
        },
        position: [1050, 300],
        typeVersion: 1,
      },
    ],
    connections: {
      'Schedule Trigger': {
        main: [
          [
            {
              node: 'Get User Config',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Get User Config': {
        main: [
          [
            {
              node: 'Generate Educational Content',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Generate Educational Content': {
        main: [
          [
            {
              node: 'Post to Facebook Page',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Post to Facebook Page': {
        main: [
          [
            {
              node: 'Log Execution',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
    },
    settings: {
      executionOrder: 'v1',
    },
  },
}

async function checkN8NConnection() {
  try {
    console.log('🔍 Checking n8n connection...')
    const response = await n8nAPI.get('/health')
    console.log('✅ n8n is running and accessible')
    return true
  } catch (error) {
    console.error('❌ Cannot connect to n8n:')
    console.error(`   URL: ${N8N_API_URL}`)
    console.error(`   Error: ${error.message}`)
    console.log('\n💡 Make sure n8n is running:')
    console.log('   npx n8n')
    return false
  }
}

async function setupWorkflowTemplate(type, template) {
  try {
    console.log(`\n📋 Setting up ${type} workflow template...`)
    
    // Check if template already exists
    const existingWorkflows = await n8nAPI.get('/workflows')
    const existing = existingWorkflows.data.find(w => w.name === template.name)
    
    if (existing) {
      console.log(`   ⚠️  Template "${template.name}" already exists (ID: ${existing.id})`)
      console.log(`   🔄 Updating existing template...`)
      
      await n8nAPI.put(`/workflows/${existing.id}`, template)
      console.log(`   ✅ Updated ${type} template`)
      return existing.id
    } else {
      console.log(`   ➕ Creating new template...`)
      
      const response = await n8nAPI.post('/workflows', template)
      console.log(`   ✅ Created ${type} template (ID: ${response.data.id})`)
      return response.data.id
    }
  } catch (error) {
    console.error(`   ❌ Failed to setup ${type} template:`)
    console.error(`      ${error.response?.data?.message || error.message}`)
    return null
  }
}

async function listWorkflows() {
  try {
    console.log('\n📋 Current workflows in n8n:')
    const response = await n8nAPI.get('/workflows')
    
    if (response.data.length === 0) {
      console.log('   (No workflows found)')
    } else {
      response.data.forEach(workflow => {
        console.log(`   • ${workflow.name} (ID: ${workflow.id}) ${workflow.active ? '🟢 Active' : '🔴 Inactive'}`)
      })
    }
  } catch (error) {
    console.error('❌ Failed to list workflows:', error.message)
  }
}

async function main() {
  console.log('🚀 Sidera Systems - n8n Workflow Setup')
  console.log('=====================================\n')
  
  // Check connection
  const connected = await checkN8NConnection()
  if (!connected) {
    process.exit(1)
  }
  
  // List current workflows
  await listWorkflows()
  
  // Setup templates
  console.log('\n🔧 Setting up workflow templates...')
  
  const templateIds = {}
  for (const [type, template] of Object.entries(WORKFLOW_TEMPLATES)) {
    const id = await setupWorkflowTemplate(type, template)
    if (id) {
      templateIds[type] = id
    }
  }
  
  // Final status
  console.log('\n✅ Setup complete!')
  console.log('\n📋 Next steps:')
  console.log('   1. Open n8n dashboard: ' + N8N_API_URL)
  console.log('   2. Review and test the workflow templates')
  console.log('   3. Configure credentials for external services')
  console.log('   4. Start your Next.js application: npm run dev')
  
  console.log('\n🔗 Template IDs for reference:')
  Object.entries(templateIds).forEach(([type, id]) => {
    if (id) {
      console.log(`   • ${type}: ${id}`)
    }
  })
}

// Handle script execution
if (require.main === module) {
  main().catch(error => {
    console.error('\n💥 Setup failed:', error.message)
    process.exit(1)
  })
}

module.exports = {
  WORKFLOW_TEMPLATES,
  setupWorkflowTemplate,
  checkN8NConnection
}