import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Star, 
  MessageSquare, 
  Share2, 
  CheckCircle,
  ArrowRight,
  Clock,
  BarChart3,
  Settings,
  Zap,
  Calendar,
  Users,
  TrendingUp,
  Shield,
  Sparkles,
  Bot,
  Smartphone,
  Globe,
  Target,
  Workflow,
  Database
} from "lucide-react"

const coreFeatures = [
  {
    icon: <Star className="h-8 w-8" />,
    title: "Review Booster",
    description: "Automatically request reviews from satisfied customers with smart timing and personalized messages.",
    features: [
      "Smart timing based on job completion",
      "SMS and email campaigns",
      "Follow-up sequences",
      "Analytics and tracking",
      "Custom CTA messages",
      "Review filtering"
    ],
    color: "text-green-400"
  },
  {
    icon: <MessageSquare className="h-8 w-8" />,
    title: "SEO Responder",
    description: "AI-powered responses to Google Business reviews that boost your SEO rankings and customer engagement.",
    features: [
      "SEO-optimized responses",
      "Brand tone matching",
      "Keyword integration",
      "Sentiment analysis",
      "Multi-language support",
      "Response templates"
    ],
    color: "text-blue-400"
  },
  {
    icon: <Share2 className="h-8 w-8" />,
    title: "Social Poster",
    description: "Automated educational content and seasonal tips posted to your social media and Google Business Profile.",
    features: [
      "Educational content creation",
      "Seasonal tips and advice",
      "Multi-platform posting",
      "Content calendar",
      "Image optimization",
      "Hashtag generation"
    ],
    color: "text-purple-400"
  }
]

const platformFeatures = [
  {
    icon: <Bot className="h-6 w-6" />,
    title: "AI-Powered Content",
    description: "Gemini AI generates personalized content that matches your brand voice and industry expertise."
  },
  {
    icon: <Calendar className="h-6 w-6" />,
    title: "Smart Scheduling",
    description: "Custom cron expressions and intelligent timing ensure your automations run when they're most effective."
  },
  {
    icon: <BarChart3 className="h-6 w-6" />,
    title: "Advanced Analytics",
    description: "Detailed insights into automation performance, ROI tracking, and customer engagement metrics."
  },
  {
    icon: <Shield className="h-6 w-6" />,
    title: "Enterprise Security",
    description: "Bank-level encryption, OAuth 2.0 authentication, and SOC 2 compliance for your peace of mind."
  },
  {
    icon: <Smartphone className="h-6 w-6" />,
    title: "Mobile Optimized",
    description: "Fully responsive dashboard that works perfectly on desktop, tablet, and mobile devices."
  },
  {
    icon: <Database className="h-6 w-6" />,
    title: "Reliable Infrastructure",
    description: "99.9% uptime with automated backups, redundancy, and real-time monitoring."
  }
]

const integrations = [
  {
    name: "Google Business Profile",
    description: "Manage reviews and posts",
    type: "OAuth",
    icon: "🏢"
  },
  {
    name: "Facebook Pages",
    description: "Automated social posting",
    type: "OAuth",
    icon: "📘"
  },
  {
    name: "SendGrid",
    description: "Email delivery service",
    type: "API Key",
    icon: "📧"
  },
  {
    name: "Twilio",
    description: "SMS messaging service",
    type: "API Key",
    icon: "📱"
  },
  {
    name: "Stripe",
    description: "Payment processing",
    type: "OAuth",
    icon: "💳"
  },
  {
    name: "Google Analytics",
    description: "Performance tracking",
    type: "OAuth",
    icon: "📊"
  }
]

export default function Features() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-900/20 via-background to-background" />
        <div className="relative mx-auto max-w-7xl">
          <div className="text-center">
            <Badge variant="secondary" className="mb-8">
              <Sparkles className="h-3 w-3 mr-2" />
              Complete Marketing Automation Platform
            </Badge>
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6 animate-fade-in">
              Everything You Need to
              <span className="block bg-gradient-to-r from-brand-400 to-brand-600 bg-clip-text text-transparent">
                Automate Marketing
              </span>
            </h1>
            <p className="mx-auto max-w-2xl text-lg text-muted-foreground mb-10 animate-fade-in">
              Comprehensive marketing automation designed specifically for home service businesses. 
              Set it up once and let AI handle your customer engagement.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in">
              <Link href="/signup">
                <Button size="xl" variant="gradient">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/demo">
                <Button size="xl" variant="outline">
                  Schedule Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Core Automations */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary/20">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold sm:text-4xl mb-4">
              Three Core Automations That Drive Results
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Each automation is designed to solve specific challenges home service businesses face every day.
            </p>
          </div>

          <div className="space-y-12">
            {coreFeatures.map((feature, index) => (
              <div key={feature.title} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}>
                <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                  <Card className="border-0 bg-card/50 backdrop-blur h-full">
                    <CardHeader>
                      <div className={`p-4 rounded-lg bg-secondary/50 w-fit ${feature.color}`}>
                        {feature.icon}
                      </div>
                      <CardTitle className="text-2xl">{feature.title}</CardTitle>
                      <CardDescription className="text-base">
                        {feature.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {feature.features.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-center gap-3">
                            <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                            <span className="text-sm">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </div>
                
                <div className={index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}>
                  <div className="bg-gradient-to-br from-secondary/50 to-secondary/20 rounded-2xl p-8 h-96">
                    <div className="text-center h-full flex items-center justify-center">
                      <div>
                        <div className={`mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-brand-600 to-brand-800 flex items-center justify-center mb-4 ${feature.color}`}>
                          {feature.icon}
                        </div>
                        <p className="text-muted-foreground">
                          Interactive demo coming soon
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Features */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold sm:text-4xl mb-4">
              Built for Professional Home Service Businesses
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Enterprise-grade features designed to scale with your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {platformFeatures.map((feature) => (
              <Card key={feature.title} className="border-0 bg-card/50 backdrop-blur hover:shadow-lg transition-all duration-200">
                <CardHeader>
                  <div className="p-3 rounded-lg bg-brand-600/10 w-fit">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Integrations */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary/20">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold sm:text-4xl mb-4">
              Seamless Integrations
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Connect with the tools you already use. Our OAuth and API integrations make setup effortless.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {integrations.map((integration) => (
              <Card key={integration.name} className="border-0 bg-card/50 backdrop-blur">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="text-3xl">{integration.icon}</div>
                    <div className="flex-1">
                      <h3 className="font-semibold">{integration.name}</h3>
                      <p className="text-sm text-muted-foreground">{integration.description}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {integration.type}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-muted-foreground mb-6">
              Don't see your tool? We're constantly adding new integrations.
            </p>
            <Button variant="outline">
              Request Integration
            </Button>
          </div>
        </div>
      </section>

      {/* Workflow Engine */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge variant="secondary" className="mb-4">
                <Workflow className="h-3 w-3 mr-2" />
                Powered by n8n
              </Badge>
              <h2 className="text-3xl font-bold sm:text-4xl mb-6">
                Enterprise Workflow Engine
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Built on n8n, the leading open-source automation platform. 
                Your workflows run on dedicated infrastructure with 99.9% uptime.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>Custom cron scheduling per user</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>Real-time workflow monitoring</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>Automatic error handling and retries</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>Detailed execution logs</span>
                </div>
              </div>

              <div className="mt-8">
                <Link href="/signup">
                  <Button size="lg" variant="gradient">
                    Get Started Now
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-secondary/50 to-secondary/20 rounded-2xl p-8">
              <div className="text-center">
                <Workflow className="h-16 w-16 mx-auto mb-4 text-brand-600" />
                <h3 className="text-xl font-semibold mb-2">Workflow Visualization</h3>
                <p className="text-muted-foreground">
                  Interactive workflow diagram coming soon
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary/20">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold sm:text-4xl mb-4">
            Ready to Automate Your Marketing?
          </h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join hundreds of home service businesses that have automated their marketing with Sidera Systems.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="xl" variant="gradient">
                Start Your Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/pricing">
              <Button size="xl" variant="outline">
                View Pricing
              </Button>
            </Link>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            No credit card required • 14-day free trial • Cancel anytime
          </p>
        </div>
      </section>
    </>
  )
}