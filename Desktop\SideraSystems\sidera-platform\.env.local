# Sidera Systems - Proof Engine Environment Configuration

# ===========================================
# SERVER CONFIGURATION (WE CONFIGURE THESE)
# ===========================================

# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Sidera Systems"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://dzwxaawlddkelgalpsjl.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR6d3hhYXdsZGRrZWxnYWxwc2psIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5OTg4NTYsImV4cCI6MjA2OTU3NDg1Nn0.1nuL0LhuvSvzjWJITU_nMK67gbC2-2p6v3VgANoZJCg
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR6d3hhYXdsZGRrZWxnYWxwc2psIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mzk5ODg1NiwiZXhwIjoyMDY5NTc0ODU2fQ.jSBH_XuU0RcheJbuzrdjjhSid48wuNJuhWiV1EgD9LY

# n8n Configuration (Our self-hosted workflow engine)
N8N_API_URL=http://localhost:5678/api/v1
N8N_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzZWZmOTRmNy05YTg4LTQyMTgtYWY3Yy1hMzRiOTRlN2IwNGMiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU0MDgzMzExfQ.A4jReTNG4KwtA9gbbow5N8fOZMG-O8zh5yKayKonDTQ

# OUR Facebook App (for Social Poster workflow)
# We create ONE Facebook app, clients OAuth to it
FACEBOOK_CLIENT_ID=1078524843896615
FACEBOOK_CLIENT_SECRET=********************************

# OUR Google OAuth App (for Google Business Profile integration)
# We create ONE Google app, clients OAuth to it
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# Google Gemini AI (for SEO responses)
GOOGLE_AI_API_KEY=your_gemini_api_key

# Stripe Configuration (Our payment processing)
STRIPE_SECRET_KEY=sk_test_51OnPTCJIdxHGVskExVtN0xpkKaVTgnAcM1AxckmwfZIPRxF7pYyfXBXAj6JIk7nx3ZJb4jABXIPjI9QO8Gz0t62P00f4bsPNPU
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51OnPTCJIdxHGVskEzbImu1OH5UZFDT5TNOAjM0gTNkhyfJzln9lyRIvkqBhdehIfkJasMJtBv66PK394xqm7y1QJ006F04a6kr
STRIPE_WEBHOOK_SECRET=4389578294734678234678923648732648

# Admin Configuration
ADMIN_EMAIL=<EMAIL>

# Security
JWT_SECRET=3084755890437589073428905789043
NEXTAUTH_SECRET=409579834758973459087349083892467920374
NEXTAUTH_URL=http://localhost:3000

# ===========================================
# CLIENT FILLS IN (Via Dashboard UI)
# ===========================================
# These are NOT in .env - clients enter these in our dashboard:
#
# 1. SEO Responder Workflow:
#    - Google Business API Key (from their Google Cloud Console)
#    - Google Business Location ID
#
# 2. Social Poster Workflow:
#    - Facebook Page Access (via OAuth to OUR Facebook app)
#    - Page selection and permissions