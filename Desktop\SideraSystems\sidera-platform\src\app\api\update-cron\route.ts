import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { n8nApi } from '@/lib/n8n'

// PATCH /api/update-cron - Updates a user's automation config and n8n workflow
export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      automationId,
      cronSchedule,
      isActive,
      settings 
    } = body

    // Validate required fields
    if (!automationId || !cronSchedule) {
      return NextResponse.json(
        { error: 'Missing required fields: automationId, cronSchedule' },
        { status: 400 }
      )
    }

    // Validate cron expression (basic validation)
    if (!isValidCronExpression(cronSchedule)) {
      return NextResponse.json(
        { error: 'Invalid cron expression format' },
        { status: 400 }
      )
    }

    // Get existing automation config with user ownership check
    const { data: automation, error: automationError } = await supabase
      .from('automation_configs')
      .select('*')
      .eq('id', automationId)
      .eq('user_id', user.id)
      .single()

    if (automationError || !automation) {
      return NextResponse.json(
        { error: 'Automation not found or access denied' },
        { status: 404 }
      )
    }

    // Update n8n workflow if it exists
    if (automation.workflow_id) {
      try {
        // Step 1: Deactivate workflow
        await n8nApi.deactivateWorkflow(automation.workflow_id)
        
        // Step 2: Update cron schedule in workflow
        await n8nApi.updateWorkflowSchedule(automation.workflow_id, cronSchedule)
        
        // Step 3: Update workflow settings if provided
        if (settings) {
          await n8nApi.updateWorkflowSettings(automation.workflow_id, settings)
        }
        
        // Step 4: Reactivate workflow if requested
        if (isActive !== false) {
          await n8nApi.activateWorkflow(automation.workflow_id)
        }
        
        // Log the workflow update
        await supabase
          .from('logs')
          .insert({
            user_id: user.id,
            automation_type: automation.automation_type,
            event_type: 'info',
            message: `Cron schedule updated to: ${cronSchedule}`,
            details: {
              old_schedule: automation.cron_schedule,
              new_schedule: cronSchedule,
              workflow_id: automation.workflow_id,
              active: isActive !== false
            }
          })

      } catch (n8nError) {
        console.error('n8n API error:', n8nError)
        
        // Log the error
        await supabase
          .from('logs')
          .insert({
            user_id: user.id,
            automation_type: automation.automation_type,
            event_type: 'error',
            message: 'Failed to update n8n workflow',
            details: { error: String(n8nError), workflow_id: automation.workflow_id }
          })

        return NextResponse.json(
          { error: 'Failed to update workflow in n8n' },
          { status: 500 }
        )
      }
    }

    // Calculate next run time
    const nextRun = isActive !== false ? calculateNextRun(cronSchedule) : null

    // Update automation config in database
    const { data: updatedAutomation, error: updateError } = await supabase
      .from('automation_configs')
      .update({
        cron_schedule: cronSchedule,
        is_active: isActive !== false,
        settings: { ...automation.settings, ...settings },
        next_run: nextRun,
        updated_at: new Date().toISOString()
      })
      .eq('id', automationId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (updateError) {
      console.error('Database update error:', updateError)
      return NextResponse.json(
        { error: 'Failed to update automation configuration' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      automation: updatedAutomation,
      message: `Cron schedule updated successfully to: ${cronSchedule}`
    })

  } catch (error) {
    console.error('Update cron API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to validate cron expression
function isValidCronExpression(cron: string): boolean {
  const parts = cron.trim().split(/\s+/)
  if (parts.length !== 5) return false
  
  // Basic validation for each part
  const patterns = [
    /^(\*|([0-5]?\d)(,([0-5]?\d))*(\/\d+)?)$/, // minute
    /^(\*|([01]?\d|2[0-3])(,([01]?\d|2[0-3]))*(\/\d+)?)$/, // hour
    /^(\*|([12]?\d|3[01])(,([12]?\d|3[01]))*(\/\d+)?)$/, // day
    /^(\*|([1-9]|1[012])(,([1-9]|1[012]))*(\/\d+)?)$/, // month
    /^(\*|[0-6](,[0-6])*(\/\d+)?)$/ // day of week
  ]
  
  return parts.every((part, index) => patterns[index].test(part))
}

// Helper function to calculate next run time
function calculateNextRun(cronSchedule: string): string {
  // This is a simplified calculation for demo
  // In production, use a proper cron parser like 'cron-parser'
  const now = new Date()
  
  // Parse basic patterns
  const [minute, hour, day, month, dayOfWeek] = cronSchedule.split(' ')
  
  if (cronSchedule === '0 9 * * *') { // Daily at 9 AM
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(9, 0, 0, 0)
    return tomorrow.toISOString()
  } else if (cronSchedule === '0 */4 * * *') { // Every 4 hours
    const next = new Date(now)
    next.setHours(next.getHours() + 4, 0, 0, 0)
    return next.toISOString()
  } else if (cronSchedule === '0 10 * * 1') { // Weekly on Monday at 10 AM
    const next = new Date(now)
    const daysUntilMonday = (1 + 7 - next.getDay()) % 7 || 7
    next.setDate(next.getDate() + daysUntilMonday)
    next.setHours(10, 0, 0, 0)
    return next.toISOString()
  }
  
  // Default: add 1 hour
  const next = new Date(now)
  next.setHours(next.getHours() + 1)
  return next.toISOString()
}