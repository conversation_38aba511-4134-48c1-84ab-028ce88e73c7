{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow-lg hover:shadow-xl transition-all duration-200\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wGACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2VACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n        success: \"border-transparent bg-green-600 text-white hover:bg-green-600/80\",\r\n        warning: \"border-transparent bg-yellow-600 text-white hover:bg-yellow-600/80\",\r\n        info: \"border-transparent bg-blue-600 text-white hover:bg-blue-600/80\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\r\nimport { Database } from './types'\r\n\r\nexport function createClient() {\r\n  return createBrowserClient<Database>(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  )\r\n}"], "names": [], "mappings": ";;;AAKI;AALJ;AAAA;;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/app/signup/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { useRouter } from \"next/navigation\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Input } from \"@/components/ui/input\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { \n  AlertCircle,\n  Loader2, \n  Mail, \n  Lock, \n  User, \n  Building, \n  MapPin,\n  CheckCircle,\n  ArrowRight\n} from \"lucide-react\"\n\nconst businessCategories = [\n  'HVAC',\n  'Plumbing', \n  'Electrical',\n  'Roofing',\n  'Landscaping',\n  'Cleaning Services',\n  'Handyman',\n  'Pest Control',\n  'Appliance Repair',\n  'Other'\n]\n\nexport default function SignUp() {\n  const [step, setStep] = useState(1)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    fullName: '',\n    businessName: '',\n    businessCategory: '',\n    serviceArea: '',\n    plan: 'standard'\n  })\n  const router = useRouter()\n  const supabase = createClient()\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    if (error) setError(null) // Clear error when user starts typing\n  }\n\n  const handleNextStep = () => {\n    if (step < 3) {\n      setStep(step + 1)\n    }\n  }\n\n  const handleSignUp = async () => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      // Create auth user\n      const { data, error: authError } = await supabase.auth.signUp({\n        email: formData.email,\n        password: formData.password,\n        options: {\n          data: {\n            full_name: formData.fullName,\n            business_name: formData.businessName,\n            business_category: formData.businessCategory,\n            service_area: formData.serviceArea,\n          }\n        }\n      })\n\n      if (authError) {\n        setError(authError.message)\n        return\n      }\n\n      if (data.user) {\n        // Create user profile in database\n        const { error: profileError } = await supabase\n          .from('client_configs')\n          .insert({\n            user_id: data.user.id,\n            business_name: formData.businessName,\n            business_category: formData.businessCategory,\n            service_area: formData.serviceArea,\n            subscription_plan: formData.plan,\n          })\n\n        if (profileError) {\n          console.error('Profile creation error:', profileError)\n          // Continue anyway - user is created\n        }\n\n        // Redirect to onboarding\n        router.push('/onboarding')\n      }\n    } catch (error) {\n      console.error('Sign up error:', error)\n      setError('An unexpected error occurred. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const plans = [\n    {\n      name: 'Basic',\n      price: 49,\n      description: 'Perfect for getting started',\n      features: [\n        '1 Automation',\n        '500 Review Requests/month',\n        'Basic Analytics',\n        'Email Support'\n      ]\n    },\n    {\n      name: 'Standard',\n      price: 99,\n      description: 'Most popular choice',\n      features: [\n        '3 Automations',\n        '2,000 Review Requests/month',\n        'Advanced Analytics',\n        'Priority Support',\n        'SEO Auto-Replies'\n      ],\n      popular: true\n    },\n    {\n      name: 'Premium',\n      price: 199,\n      description: 'For established businesses',\n      features: [\n        'Unlimited Automations',\n        'Unlimited Review Requests',\n        'Full Analytics Suite',\n        '24/7 Support',\n        'Custom Workflows'\n      ]\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-slate-950 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      {/* Background effects */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-950 to-slate-900\" />\n      <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(120,119,198,0.1),transparent_50%)]\" />\n      <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_70%_60%,rgba(16,185,129,0.1),transparent_50%)]\" />\n      \n      <div className=\"max-w-md w-full space-y-10 relative z-10\">\n        <div className=\"text-center\">\n          <Link href=\"/\" className=\"flex items-center justify-center space-x-3 mb-10\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent\">\n                Proof Engine\n              </div>\n              <div className=\"text-sm text-slate-400 font-medium\">\n                by Sidera Systems\n              </div>\n            </div>\n          </Link>\n          \n          <h2 className=\"text-4xl font-bold bg-gradient-to-r from-white via-slate-100 to-slate-300 bg-clip-text text-transparent mb-3\">\n            Create your account\n          </h2>\n          <p className=\"text-lg text-slate-400 leading-relaxed\">\n            Start building your AI marketing engine today\n          </p>\n        </div>\n\n        <Card className=\"border-0 bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-xl border border-slate-700/50 shadow-2xl\">\n          <CardHeader className=\"pb-6\">\n            <div className=\"flex items-center justify-between\">\n              <CardTitle className=\"text-xl font-semibold text-slate-100\">\n                Step {step} of 3\n              </CardTitle>\n              <div className=\"flex space-x-3\">\n                {[1, 2, 3].map((i) => (\n                  <div\n                    key={i}\n                    className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                      i <= step \n                        ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 shadow-lg shadow-emerald-500/25' \n                        : 'bg-slate-700'\n                    }`}\n                  />\n                ))}\n              </div>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"space-y-6\">\n            {step === 1 && (\n              <div className=\"space-y-6\">\n                <div className=\"text-center mb-8\">\n                  <h3 className=\"text-2xl font-semibold text-slate-100 mb-2\">Account Details</h3>\n                  <p className=\"text-slate-400 leading-relaxed\">\n                    Let's start with your basic information\n                  </p>\n                </div>\n\n                {error && (\n                  <div className=\"flex items-center gap-3 p-4 bg-red-950/50 border border-red-900/50 rounded-lg\">\n                    <AlertCircle className=\"h-5 w-5 text-red-400 flex-shrink-0\" />\n                    <p className=\"text-red-300 text-sm\">{error}</p>\n                  </div>\n                )}\n\n                <div className=\"space-y-5\">\n                  <div className=\"relative\">\n                    <User className=\"absolute left-4 top-4 h-5 w-5 text-slate-500\" />\n                    <Input\n                      placeholder=\"Full Name\"\n                      value={formData.fullName}\n                      onChange={(e) => handleInputChange('fullName', e.target.value)}\n                      className=\"pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg\"\n                    />\n                  </div>\n                  \n                  <div className=\"relative\">\n                    <Mail className=\"absolute left-4 top-4 h-5 w-5 text-slate-500\" />\n                    <Input\n                      type=\"email\"\n                      placeholder=\"Email Address\"\n                      value={formData.email}\n                      onChange={(e) => handleInputChange('email', e.target.value)}\n                      className=\"pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg\"\n                    />\n                  </div>\n                  \n                  <div className=\"relative\">\n                    <Lock className=\"absolute left-4 top-4 h-5 w-5 text-slate-500\" />\n                    <Input\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={formData.password}\n                      onChange={(e) => handleInputChange('password', e.target.value)}\n                      className=\"pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg\"\n                    />\n                  </div>\n                </div>\n\n                <Button \n                  onClick={handleNextStep}\n                  className=\"w-full h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-lg shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-[1.02] hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                  disabled={!formData.fullName || !formData.email || !formData.password}\n                >\n                  Continue\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Button>\n              </div>\n            )}\n\n            {step === 2 && (\n              <div className=\"space-y-6\">\n                <div className=\"text-center mb-8\">\n                  <h3 className=\"text-2xl font-semibold text-slate-100 mb-2\">Business Information</h3>\n                  <p className=\"text-slate-400 leading-relaxed\">\n                    Tell us about your business\n                  </p>\n                </div>\n\n                <div className=\"space-y-5\">\n                  <div className=\"relative\">\n                    <Building className=\"absolute left-4 top-4 h-5 w-5 text-slate-500\" />\n                    <Input\n                      placeholder=\"Business Name\"\n                      value={formData.businessName}\n                      onChange={(e) => handleInputChange('businessName', e.target.value)}\n                      className=\"pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg\"\n                    />\n                  </div>\n                  \n                  <div className=\"relative\">\n                    <select\n                      value={formData.businessCategory}\n                      onChange={(e) => handleInputChange('businessCategory', e.target.value)}\n                      className=\"h-12 w-full rounded-lg border border-slate-700/50 bg-slate-800/50 px-4 py-3 text-slate-100 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/20 focus-visible:border-emerald-500 disabled:cursor-not-allowed disabled:opacity-50\"\n                    >\n                      <option value=\"\" className=\"bg-slate-800 text-slate-400\">Select Business Category</option>\n                      {businessCategories.map(category => (\n                        <option key={category} value={category} className=\"bg-slate-800 text-slate-100\">{category}</option>\n                      ))}\n                    </select>\n                  </div>\n                  \n                  <div className=\"relative\">\n                    <MapPin className=\"absolute left-4 top-4 h-5 w-5 text-slate-500\" />\n                    <Input\n                      placeholder=\"Service Area (e.g., Chicago, IL)\"\n                      value={formData.serviceArea}\n                      onChange={(e) => handleInputChange('serviceArea', e.target.value)}\n                      className=\"pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg\"\n                    />\n                  </div>\n                </div>\n\n                <Button \n                  onClick={handleNextStep}\n                  className=\"w-full h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-lg shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-[1.02] hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                  disabled={!formData.businessName || !formData.businessCategory || !formData.serviceArea}\n                >\n                  Continue\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Button>\n              </div>\n            )}\n\n            {step === 3 && (\n              <div className=\"space-y-6\">\n                <div className=\"text-center mb-8\">\n                  <h3 className=\"text-2xl font-semibold text-slate-100 mb-2\">Choose Your Plan</h3>\n                  <p className=\"text-slate-400 leading-relaxed\">\n                    Start with a 14-day free trial\n                  </p>\n                </div>\n\n                <div className=\"grid gap-5\">\n                  {plans.map((plan) => (\n                    <div\n                      key={plan.name}\n                      className={`relative border rounded-xl p-6 cursor-pointer transition-all duration-300 ${\n                        formData.plan === plan.name.toLowerCase()\n                          ? 'border-emerald-500 bg-gradient-to-br from-emerald-900/20 to-emerald-800/10 shadow-lg shadow-emerald-500/25'\n                          : 'border-slate-700/50 bg-slate-800/30 hover:border-slate-600 hover:bg-slate-800/50'\n                      }`}\n                      onClick={() => handleInputChange('plan', plan.name.toLowerCase())}\n                    >\n                      {plan.popular && (\n                        <Badge className=\"absolute -top-3 left-6 bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-lg\">\n                          Most Popular\n                        </Badge>\n                      )}\n                      \n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h4 className=\"font-semibold text-slate-100 text-lg\">{plan.name}</h4>\n                        <div className=\"text-right\">\n                          <span className=\"text-3xl font-bold text-slate-100\">${plan.price}</span>\n                          <span className=\"text-sm text-slate-400\">/mo</span>\n                        </div>\n                      </div>\n                      \n                      <p className=\"text-slate-400 mb-4 leading-relaxed\">{plan.description}</p>\n                      \n                      <ul className=\"space-y-2\">\n                        {plan.features.map((feature, index) => (\n                          <li key={index} className=\"flex items-center text-sm text-slate-300\">\n                            <CheckCircle className=\"h-4 w-4 text-emerald-400 mr-3 flex-shrink-0\" />\n                            {feature}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  ))}\n                </div>\n\n                <Button \n                  onClick={handleSignUp}\n                  className=\"w-full h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-lg shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-[1.02] hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                  disabled={loading}\n                >\n                  {loading ? (\n                    <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                  ) : (\n                    'Start Free Trial'\n                  )}\n                </Button>\n\n                <p className=\"text-sm text-center text-slate-400\">\n                  No credit card required • Cancel anytime\n                </p>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        <div className=\"text-center\">\n          <p className=\"text-slate-400\">\n            Already have an account?{' '}\n            <Link href=\"/login\" className=\"font-medium text-emerald-400 hover:text-emerald-300 transition-colors\">\n              Sign in\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAsBA,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,UAAU;QACV,cAAc;QACd,kBAAkB;QAClB,aAAa;QACb,MAAM;IACR;IACA,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,OAAO,SAAS,OAAM,sCAAsC;IAClE;IAEA,MAAM,iBAAiB;QACrB,IAAI,OAAO,GAAG;YACZ,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QAET,IAAI;YACF,mBAAmB;YACnB,MAAM,EAAE,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC5D,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,SAAS;oBACP,MAAM;wBACJ,WAAW,SAAS,QAAQ;wBAC5B,eAAe,SAAS,YAAY;wBACpC,mBAAmB,SAAS,gBAAgB;wBAC5C,cAAc,SAAS,WAAW;oBACpC;gBACF;YACF;YAEA,IAAI,WAAW;gBACb,SAAS,UAAU,OAAO;gBAC1B;YACF;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,kCAAkC;gBAClC,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,kBACL,MAAM,CAAC;oBACN,SAAS,KAAK,IAAI,CAAC,EAAE;oBACrB,eAAe,SAAS,YAAY;oBACpC,mBAAmB,SAAS,gBAAgB;oBAC5C,cAAc,SAAS,WAAW;oBAClC,mBAAmB,SAAS,IAAI;gBAClC;gBAEF,IAAI,cAAc;oBAChB,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,oCAAoC;gBACtC;gBAEA,yBAAyB;gBACzB,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA4F;;;;;;sDAG3G,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;;;;;;;;;;;;0CAMxD,6LAAC;gCAAG,WAAU;0CAA+G;;;;;;0CAG7H,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;kCAKxD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;gDAAuC;gDACpD;gDAAK;;;;;;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;oDAEC,WAAW,AAAC,oDAIX,OAHC,KAAK,OACD,qFACA;mDAJD;;;;;;;;;;;;;;;;;;;;;0CAYf,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,SAAS,mBACR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;;;;;;;4CAK/C,uBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC7D,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gEAC1D,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC7D,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,WAAU;gDACV,UAAU,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ;;oDACtE;kEAEC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;oCAK3B,SAAS,mBACR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;;;;;;;0DAKhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,YAAY;gEAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEACjE,WAAU;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,OAAO,SAAS,gBAAgB;4DAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4DACrE,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;oEAAG,WAAU;8EAA8B;;;;;;gEACxD,mBAAmB,GAAG,CAAC,CAAA,yBACtB,6LAAC;wEAAsB,OAAO;wEAAU,WAAU;kFAA+B;uEAApE;;;;;;;;;;;;;;;;kEAKnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC,oIAAA,CAAA,QAAK;gEACJ,aAAY;gEACZ,OAAO,SAAS,WAAW;gEAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gEAChE,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,WAAU;gDACV,UAAU,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,gBAAgB,IAAI,CAAC,SAAS,WAAW;;oDACxF;kEAEC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;oCAK3B,SAAS,mBACR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAiC;;;;;;;;;;;;0DAKhD,6LAAC;gDAAI,WAAU;0DACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wDAEC,WAAW,AAAC,6EAIX,OAHC,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,KACnC,+GACA;wDAEN,SAAS,IAAM,kBAAkB,QAAQ,KAAK,IAAI,CAAC,WAAW;;4DAE7D,KAAK,OAAO,kBACX,6LAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAqG;;;;;;0EAKxH,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAwC,KAAK,IAAI;;;;;;kFAC/D,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;;oFAAoC;oFAAE,KAAK,KAAK;;;;;;;0FAChE,6LAAC;gFAAK,WAAU;0FAAyB;;;;;;;;;;;;;;;;;;0EAI7C,6LAAC;gEAAE,WAAU;0EAAuC,KAAK,WAAW;;;;;;0EAEpE,6LAAC;gEAAG,WAAU;0EACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,6LAAC;wEAAe,WAAU;;0FACxB,6LAAC,8NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB;;uEAFM;;;;;;;;;;;uDA1BR,KAAK,IAAI;;;;;;;;;;0DAoCpB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,WAAU;gDACV,UAAU;0DAET,wBACC,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;2DAEnB;;;;;;0DAIJ,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAiB;gCACH;8CACzB,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlH;GA3WwB;;QAaP,qIAAA,CAAA,YAAS;;;KAbF", "debugId": null}}]}