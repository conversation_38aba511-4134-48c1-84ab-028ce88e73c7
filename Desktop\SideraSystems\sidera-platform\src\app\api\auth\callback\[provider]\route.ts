import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// OAuth callback handler for different providers
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ provider: string }> }
) {
  const { provider } = await params
  const searchParams = request.nextUrl.searchParams
  const code = searchParams.get('code')
  const state = searchParams.get('state')
  const error = searchParams.get('error')

  // Handle OAuth errors
  if (error) {
    console.error('OAuth error:', error)
    return NextResponse.redirect(
      new URL(`/dashboard?error=${encodeURIComponent(error)}`, request.url)
    )
  }

  if (!code || !state) {
    return NextResponse.redirect(
      new URL('/dashboard?error=missing_code_or_state', request.url)
    )
  }

  try {
    // Parse state to get workflow info
    const { workflowType, providerId } = JSON.parse(state)
    
    // Exchange code for access token based on provider
    let tokenData
    let userInfo
    
    switch (provider) {
      case 'facebook':
        tokenData = await exchangeFacebookCode(code)
        userInfo = await getFacebookUserInfo(tokenData.access_token)
        break
      case 'google_business':
        tokenData = await exchangeGoogleCode(code)
        userInfo = await getGoogleUserInfo(tokenData.access_token)
        break
      default:
        throw new Error(`Unsupported provider: ${provider}`)
    }

    // Get current user from Supabase
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      throw new Error('User not authenticated')
    }

    // Store auth configuration in database
    const authConfig = {
      user_id: user.id,
      workflow_type: workflowType,
      provider: providerId,
      auth_method: 'oauth' as const,
      auth_data: {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_at: tokenData.expires_in 
          ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
          : null,
        scope: tokenData.scope?.split(',') || [],
        provider_user_id: userInfo.id,
        provider_username: userInfo.name || userInfo.username
      },
      status: 'connected' as const,
      expires_at: tokenData.expires_in 
        ? new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
        : null,
      provider_user_id: userInfo.id,
      provider_username: userInfo.name || userInfo.username
    }

    // Upsert the auth config (update if exists, insert if new)
    const { error: dbError } = await supabase
      .from('workflow_auth_configs')
      .upsert(authConfig, {
        onConflict: 'user_id,workflow_type,provider'
      })

    if (dbError) {
      console.error('Database error:', dbError)
      throw new Error('Failed to save authentication')
    }

    // Close popup and redirect parent
    return new NextResponse(
      `
      <html>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({ success: true, provider: '${provider}' }, '*');
              window.close();
            } else {
              window.location.href = '/dashboard?success=connected_${provider}';
            }
          </script>
        </body>
      </html>
      `,
      {
        headers: { 'Content-Type': 'text/html' }
      }
    )

  } catch (error) {
    console.error('OAuth callback error:', error)
    return NextResponse.redirect(
      new URL(`/dashboard?error=${encodeURIComponent(String(error))}`, request.url)
    )
  }
}

// Facebook token exchange
async function exchangeFacebookCode(code: string) {
  const response = await fetch('https://graph.facebook.com/v18.0/oauth/access_token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      client_id: process.env.FACEBOOK_CLIENT_ID || '',
      client_secret: process.env.FACEBOOK_CLIENT_SECRET || '',
      code,
      redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/auth/callback/facebook`
    })
  })

  if (!response.ok) {
    throw new Error('Failed to exchange Facebook code')
  }

  return response.json()
}

// Get Facebook user info
async function getFacebookUserInfo(accessToken: string) {
  const response = await fetch(
    `https://graph.facebook.com/me?fields=id,name,accounts{id,name,access_token}&access_token=${accessToken}`
  )

  if (!response.ok) {
    throw new Error('Failed to get Facebook user info')
  }

  return response.json()
}

// Google token exchange
async function exchangeGoogleCode(code: string) {
  const response = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      client_id: process.env.GOOGLE_CLIENT_ID || '',
      client_secret: process.env.GOOGLE_CLIENT_SECRET || '',
      code,
      grant_type: 'authorization_code',
      redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/api/auth/callback/google_business`
    })
  })

  if (!response.ok) {
    throw new Error('Failed to exchange Google code')
  }

  return response.json()
}

// Get Google user info
async function getGoogleUserInfo(accessToken: string) {
  const response = await fetch(
    `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${accessToken}`
  )

  if (!response.ok) {
    throw new Error('Failed to get Google user info')
  }

  return response.json()
}