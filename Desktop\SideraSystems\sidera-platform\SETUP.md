# 🚀 Sidera Systems - Quick Setup Guide

Get your AI Marketing Automation platform running in **5 minutes**!

## 📋 **Prerequisites**

- **Node.js 18+** (check: `node --version`)
- **npm or yarn**
- **Git**

## ⚡ **Quick Start (Development)**

### **Step 1: Environment Setup**
```bash
# 1. Copy environment template
cp .env.example .env.local

# 2. Check environment
npm run check:env
```

### **Step 2: Install Dependencies**
```bash
npm install
```

### **Step 3: Start n8n + Next.js**
```bash
# Option A: Start both together (recommended)
npm run dev:full

# Option B: Start separately (for debugging)
# Terminal 1: npx n8n
# Terminal 2: npm run dev
```

### **Step 4: Setup n8n Workflows**
```bash
# In a new terminal
npm run setup:n8n
```

### **Step 5: Open Applications**
- **Next.js Platform**: http://localhost:3000
- **n8n Dashboard**: http://localhost:5678

---

## 🛠️ **Detailed Configuration**

### **Environment Variables (`.env.local`)**

#### **Required for Development:**
```bash
# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Supabase (Create free account at supabase.com)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# n8n (Auto-configured for local development)
N8N_API_URL=http://localhost:5678
N8N_API_KEY=test

# Stripe (Create account at stripe.com)
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

#### **Required for Production:**
```bash
# Facebook OAuth (Create app at developers.facebook.com)
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret

# Google OAuth (Create app at console.cloud.google.com)
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# Google Gemini AI (Get key at ai.google.dev)
GOOGLE_AI_API_KEY=your_gemini_api_key
```

---

## 🗄️ **Database Setup (Supabase)**

### **1. Create Supabase Project**
1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Copy URL and keys to `.env.local`

### **2. Create Database Tables**
Run this SQL in Supabase SQL Editor:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Profiles table
CREATE TABLE profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  business_name TEXT,
  business_category TEXT,
  business_location TEXT,
  subscription_tier TEXT CHECK (subscription_tier IN ('basic', 'standard', 'premium')),
  subscription_status TEXT CHECK (subscription_status IN ('active', 'cancelled', 'past_due')),
  stripe_customer_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client configs table
CREATE TABLE client_configs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  google_business_api_key TEXT,
  google_business_location_id TEXT,
  preferred_tone TEXT CHECK (preferred_tone IN ('friendly', 'professional', 'casual')),
  contact_phone TEXT,
  contact_email TEXT,
  business_hours JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automation configs table
CREATE TABLE automation_configs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  automation_type TEXT CHECK (automation_type IN ('seo_responder', 'social_poster')) NOT NULL,
  is_active BOOLEAN DEFAULT false,
  cron_schedule TEXT DEFAULT '0 9 * * *',
  workflow_id TEXT,
  settings JSONB DEFAULT '{}',
  last_run TIMESTAMP WITH TIME ZONE,
  next_run TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow auth configs table
CREATE TABLE workflow_auth_configs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  workflow_type TEXT CHECK (workflow_type IN ('seo_responder', 'social_poster')) NOT NULL,
  provider TEXT NOT NULL,
  auth_method TEXT CHECK (auth_method IN ('oauth', 'api_key', 'webhook', 'email_password')) NOT NULL,
  auth_data JSONB NOT NULL,
  status TEXT CHECK (status IN ('connected', 'expired', 'error', 'pending')) DEFAULT 'pending',
  expires_at TIMESTAMP WITH TIME ZONE,
  provider_user_id TEXT,
  provider_username TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Logs table
CREATE TABLE logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  automation_type TEXT CHECK (automation_type IN ('seo_responder', 'social_poster')) NOT NULL,
  event_type TEXT CHECK (event_type IN ('success', 'error', 'warning', 'info')) NOT NULL,
  message TEXT NOT NULL,
  details JSONB,
  workflow_execution_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Posts table
CREATE TABLE posts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  automation_type TEXT CHECK (automation_type IN ('seo_responder', 'social_poster')) NOT NULL,
  platform TEXT NOT NULL,
  content TEXT NOT NULL,
  platform_post_id TEXT,
  status TEXT CHECK (status IN ('draft', 'published', 'failed')) DEFAULT 'draft',
  scheduled_for TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  engagement_metrics JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_automation_configs_user_id ON automation_configs(user_id);
CREATE INDEX idx_workflow_auth_configs_user_id ON workflow_auth_configs(user_id);
CREATE INDEX idx_logs_user_id ON logs(user_id);
CREATE INDEX idx_logs_created_at ON logs(created_at);
CREATE INDEX idx_posts_user_id ON posts(user_id);
```

### **3. Enable Row Level Security (RLS)**
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_auth_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;

-- Create policies (users can only access their own data)
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can manage own configs" ON client_configs FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own automations" ON automation_configs FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own auth configs" ON workflow_auth_configs FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own logs" ON logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own posts" ON posts FOR ALL USING (auth.uid() = user_id);
```

---

## 🔧 **n8n Configuration**

### **1. Basic Setup**
n8n will start automatically with `npm run dev:full`. The setup script will:
- Check n8n connection
- Deploy workflow templates
- Configure basic settings

### **2. Manual n8n Setup (if needed)**
```bash
# Start n8n manually
npx n8n

# Access dashboard
open http://localhost:5678

# Deploy workflows
npm run setup:n8n
```

### **3. Configure Credentials**
In n8n dashboard, add these credentials:
- **Google OAuth2**: For Google Business Profile integration
- **Facebook Graph API**: For social media posting
- **HTTP Authorization**: For internal API calls

---

## 💳 **Stripe Setup**

### **1. Create Stripe Account**
1. Go to [stripe.com](https://stripe.com)
2. Create account and get API keys
3. Add keys to `.env.local`

### **2. Create Products**
```bash
# In Stripe Dashboard, create these products:
# - Basic Plan: $49/month
# - Standard Plan: $99/month  
# - Premium Plan: $199/month
```

### **3. Configure Webhooks**
- **Endpoint**: `https://yourdomain.com/api/webhooks/stripe`
- **Events**: `customer.subscription.created`, `customer.subscription.updated`, `customer.subscription.deleted`

---

## 🔐 **OAuth App Setup**

### **Facebook App (for Social Poster)**
1. Go to [developers.facebook.com](https://developers.facebook.com)
2. Create new app
3. Add Facebook Login product
4. Configure OAuth redirect: `https://yourdomain.com/api/auth/callback/facebook`
5. Request permissions: `pages_manage_posts`, `pages_read_engagement`

### **Google OAuth App (for SEO Responder)**
1. Go to [console.cloud.google.com](https://console.cloud.google.com)
2. Create new project
3. Enable Google My Business API
4. Create OAuth 2.0 credentials
5. Configure redirect: `https://yourdomain.com/api/auth/callback/google`

---

## 🌐 **Production Deployment**

### **Phase 2: MVP (Vercel + zrok)**
```bash
# 1. Deploy to Vercel
vercel --prod

# 2. Setup zrok tunnel for n8n
# On your local machine:
zrok share public http://localhost:5678

# 3. Update Vercel environment variables
N8N_API_URL=https://your-zrok-share-url.share.zrok.io
```

### **Phase 3: Scale (Vercel + Railway)**
```bash
# 1. Deploy n8n to Railway
railway login
railway new
railway link
railway deploy

# 2. Update Vercel environment variables
N8N_API_URL=https://your-app.railway.app
```

---

## 🧪 **Testing & Verification**

### **1. Environment Check**
```bash
npm run check:env
```

### **2. n8n Connection Test**
```bash
npm run setup:n8n
```

### **3. Full Application Test**
1. Open http://localhost:3000
2. Sign up for account
3. Complete onboarding
4. Configure automation
5. Check n8n dashboard for workflow creation

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **n8n Connection Failed**
```bash
# Check if n8n is running
curl http://localhost:5678/health

# Restart n8n
npx n8n
```

#### **Supabase Connection Issues**
```bash
# Verify environment variables
npm run check:env

# Check Supabase dashboard for project status
```

#### **Missing Dependencies**
```bash
# Reinstall all packages
rm -rf node_modules package-lock.json
npm install
```

#### **Port Conflicts**
```bash
# Check what's using port 3000/5678
lsof -i :3000
lsof -i :5678

# Kill processes if needed
kill -9 PID
```

---

## 📚 **Next Steps**

1. **Complete Environment Setup**: Fill in all required API keys
2. **Test Workflows**: Create test user and run automations
3. **Configure OAuth Apps**: Set up Facebook and Google integrations
4. **Deploy to Production**: Follow Phase 2 or Phase 3 deployment guides
5. **Monitor & Scale**: Use admin dashboard to monitor users and workflows

---

## 🆘 **Support**

- **Documentation**: See `ARCHITECTURE.md` for technical details
- **Issues**: Check existing workflows in n8n dashboard
- **Environment**: Use `npm run check:env` for diagnostics

---

**🎉 You're all set! Your AI Marketing Automation platform is ready to go!**