import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Zap, 
  MessageSquare, 
  Star, 
  Share2, 
  CheckCircle,
  ArrowRight,
  Play,
  Users,
  TrendingUp,
  <PERSON>rk<PERSON>
} from "lucide-react"

export default function Home() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative min-h-[90vh] flex items-center justify-center px-4 sm:px-6 lg:px-8 overflow-hidden pt-20">
        {/* Enhanced background layers */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800/95 to-slate-900" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(16,185,129,0.1),transparent_50%)]" />
        
        {/* Floating elements with depth */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-emerald-500/20 to-blue-500/20 rounded-full blur-3xl animate-pulse opacity-60" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-purple-500/15 to-emerald-500/15 rounded-full blur-3xl animate-pulse delay-1000 opacity-40" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-slate-500/10 to-transparent rounded-full blur-3xl animate-pulse delay-500" />
        
        <div className="relative mx-auto max-w-6xl">
          <div className="text-center space-y-12">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-500/30 backdrop-blur-sm">
              <Sparkles className="h-4 w-4 text-emerald-400 mr-2" />
              <span className="text-sm font-semibold text-emerald-300">AI-Powered Marketing Engine</span>
            </div>
            
            <div className="space-y-8">
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold tracking-tight leading-tight animate-fade-in">
                <span className="block bg-gradient-to-r from-white via-slate-100 to-slate-300 bg-clip-text text-transparent">
                  Marketing That
                </span>
                <span className="block bg-gradient-to-r from-emerald-400 via-emerald-300 to-blue-400 bg-clip-text text-transparent">
                  Never Sleeps
                </span>
              </h1>
              
              <p className="mx-auto max-w-3xl text-xl md:text-2xl text-slate-300 leading-relaxed font-medium animate-fade-in">
                Your AI marketing team works 24/7. Automated review responses, social posts, and lead generation—all while you focus on growing your business.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in">
              <Link href="/signup">
                <Button 
                  size="xl" 
                  className="px-10 py-6 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-xl shadow-2xl shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-105 hover:shadow-emerald-500/40 text-lg"
                >
                  Start Building Your Engine
                  <ArrowRight className="ml-3 h-5 w-5" />
                </Button>
              </Link>
              <Button 
                variant="outline" 
                size="xl" 
                className="px-10 py-6 border-slate-600 text-slate-300 hover:bg-slate-800/50 hover:border-slate-500 rounded-xl font-semibold transition-all duration-300 text-lg"
              >
                <Play className="mr-3 h-5 w-5" />
                See It In Action
              </Button>
            </div>
            
            {/* Enhanced Social Proof */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto animate-fade-in">
              <div className="flex items-center justify-center space-x-3 p-5 rounded-xl bg-slate-800/40 border border-slate-700/50 backdrop-blur transition-all duration-300 hover:bg-slate-800/60">
                <Star className="h-5 w-5 fill-amber-400 text-amber-400" />
                <span className="text-slate-300 font-medium">4.9/5 from 500+ businesses</span>
              </div>
              <div className="flex items-center justify-center space-x-3 p-5 rounded-xl bg-slate-800/40 border border-slate-700/50 backdrop-blur transition-all duration-300 hover:bg-slate-800/60">
                <Users className="h-5 w-5 text-emerald-400" />
                <span className="text-slate-300 font-medium">10,000+ automations running</span>
              </div>
              <div className="flex items-center justify-center space-x-3 p-5 rounded-xl bg-slate-800/40 border border-slate-700/50 backdrop-blur transition-all duration-300 hover:bg-slate-800/60">
                <TrendingUp className="h-5 w-5 text-blue-400" />
                <span className="text-slate-300 font-medium">3x more reviews on average</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-32 px-4 sm:px-6 lg:px-8 bg-slate-950/50">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-slate-100 to-slate-300 bg-clip-text text-transparent">
              Two Core Automations That Drive Results
            </h2>
            <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
              Our AI handles the marketing tasks that matter most for home service businesses. Set it once, and watch your engagement soar.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* SEO Responder */}
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-blue-900/20 hover:from-slate-800/80 hover:via-slate-800/60 hover:to-blue-900/30 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-blue-500/20">
              <div className="absolute inset-0 bg-gradient-to-br from-slate-900/20 via-transparent to-slate-900/40 pointer-events-none" />
              
              <CardHeader className="relative pb-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="relative p-4 rounded-2xl bg-gradient-to-br from-blue-600/20 to-blue-800/20 border border-blue-500/30 backdrop-blur">
                    <MessageSquare className="h-7 w-7 text-blue-400" />
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-400/10 to-transparent pointer-events-none" />
                  </div>
                  <Badge className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white border-0 shadow-lg px-3 py-1">
                    AI Powered
                  </Badge>
                </div>
                <CardTitle className="text-2xl font-bold text-slate-100 mb-3">SEO Responder</CardTitle>
                <CardDescription className="text-slate-400 text-lg leading-relaxed">
                  AI-powered responses to Google Business reviews that boost your SEO rankings and customer engagement automatically.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="relative space-y-4">
                {[
                  "AI-generated SEO-optimized responses",
                  "Brand tone matching",
                  "Keyword integration for local SEO", 
                  "Automated review monitoring"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 rounded-xl bg-slate-800/30 border border-slate-700/30 backdrop-blur transition-all duration-300 hover:bg-slate-800/50">
                    <div className="flex-shrink-0 w-6 h-6 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-slate-300 font-medium">{feature}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Social Poster */}
            <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-purple-900/20 hover:from-slate-800/80 hover:via-slate-800/60 hover:to-purple-900/30 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/20">
              <div className="absolute inset-0 bg-gradient-to-br from-slate-900/20 via-transparent to-slate-900/40 pointer-events-none" />
              
              <CardHeader className="relative pb-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="relative p-4 rounded-2xl bg-gradient-to-br from-purple-600/20 to-purple-800/20 border border-purple-500/30 backdrop-blur">
                    <Share2 className="h-7 w-7 text-purple-400" />
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-purple-400/10 to-transparent pointer-events-none" />
                  </div>
                  <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-lg px-3 py-1">
                    Most Popular
                  </Badge>
                </div>
                <CardTitle className="text-2xl font-bold text-slate-100 mb-3">Social Poster</CardTitle>
                <CardDescription className="text-slate-400 text-lg leading-relaxed">
                  Automated posting to your Facebook business page with educational content and seasonal tips that keep customers engaged.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="relative space-y-4">
                {[
                  "Educational content creation",
                  "Seasonal tips and advice",
                  "Facebook Business Page posting",
                  "Custom scheduling"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 rounded-xl bg-slate-800/30 border border-slate-700/30 backdrop-blur transition-all duration-300 hover:bg-slate-800/50">
                    <div className="flex-shrink-0 w-6 h-6 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-slate-300 font-medium">{feature}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold sm:text-4xl mb-4">
              Set It Up Once, Let AI Do The Rest
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our proven 3-step process gets you up and running in minutes, not hours.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-brand-600 to-brand-800 text-white text-xl font-bold mb-4">
                1
              </div>
              <h3 className="text-xl font-semibold mb-2">Connect Your Business</h3>
              <p className="text-muted-foreground">
                Link your Google Business profile and choose your automations. Takes 2 minutes.
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-brand-600 to-brand-800 text-white text-xl font-bold mb-4">
                2
              </div>
              <h3 className="text-xl font-semibold mb-2">Set Your Schedule</h3>
              <p className="text-muted-foreground">
                Choose when and how often each automation runs. Daily, weekly, or custom timing.
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-brand-600 to-brand-800 text-white text-xl font-bold mb-4">
                3
              </div>
              <h3 className="text-xl font-semibold mb-2">Watch It Work</h3>
              <p className="text-muted-foreground">
                Monitor results in real-time. See every review request, response, and social post.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary/20">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold sm:text-4xl mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Choose the plan that fits your business. No hidden fees, no long-term contracts.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Basic Plan */}
            <Card className="border-0 bg-card/50 backdrop-blur">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">Basic</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">$49</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
                <CardDescription>Perfect for getting started</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">1 Automation</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">500 Review Requests/month</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Basic Analytics</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Email Support</span>
                  </li>
                </ul>
                <Button className="w-full" variant="outline">
                  Start Free Trial
                </Button>
              </CardContent>
            </Card>

            {/* Standard Plan */}
            <Card className="border-2 border-brand-600 bg-card/50 backdrop-blur relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge variant="default" className="bg-brand-600">Most Popular</Badge>
              </div>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">Standard</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">$99</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
                <CardDescription>For growing businesses</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">3 Automations</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">2,000 Review Requests/month</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Advanced Analytics</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Priority Support</span>
          </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">SEO Auto-Replies</span>
          </li>
                </ul>
                <Button className="w-full" variant="gradient">
                  Start Free Trial
                </Button>
              </CardContent>
            </Card>

            {/* Premium Plan */}
            <Card className="border-0 bg-card/50 backdrop-blur">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">Premium</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">$199</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
                <CardDescription>For established businesses</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Unlimited Automations</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Unlimited Review Requests</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Full Analytics Suite</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">24/7 Support</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-sm">Custom Workflows</span>
                  </li>
                </ul>
                <Button className="w-full" variant="outline">
                  Start Free Trial
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold sm:text-4xl mb-4">
            Ready to Automate Your Marketing?
          </h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join thousands of home service businesses that have automated their marketing and 3x'd their review generation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button size="xl" variant="gradient" className="w-full sm:w-auto">
                Start Your Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button size="xl" variant="outline" className="w-full sm:w-auto">
              Schedule a Demo
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            No credit card required • 14-day free trial • Cancel anytime
          </p>
    </div>
      </section>
    </>
  )
}