{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow-lg hover:shadow-xl transition-all duration-200\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wGACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n        success: \"border-transparent bg-green-600 text-white hover:bg-green-600/80\",\r\n        warning: \"border-transparent bg-yellow-600 text-white hover:bg-yellow-600/80\",\r\n        info: \"border-transparent bg-blue-600 text-white hover:bg-blue-600/80\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2VACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/lib/types/auth.ts"], "sourcesContent": ["// Authentication types for different workflow providers\r\nexport type AuthMethod = 'oauth' | 'api_key' | 'webhook' | 'email_password'\r\n\r\nexport interface BaseAuthConfig {\r\n  id: string\r\n  user_id: string\r\n  workflow_type: 'review_booster' | 'seo_responder' | 'social_poster'\r\n  provider: string\r\n  auth_method: AuthMethod\r\n  status: 'connected' | 'expired' | 'error' | 'pending'\r\n  created_at: string\r\n  updated_at: string\r\n}\r\n\r\n// OAuth configuration (e.g., Facebook, Google)\r\nexport interface OAuthConfig extends BaseAuthConfig {\r\n  auth_method: 'oauth'\r\n  access_token: string\r\n  refresh_token?: string\r\n  expires_at?: string\r\n  scope: string[]\r\n  provider_user_id: string\r\n  provider_username?: string\r\n}\r\n\r\n// API Key configuration (e.g., SendGrid, Twilio)\r\nexport interface ApiKeyConfig extends BaseAuthConfig {\r\n  auth_method: 'api_key'\r\n  api_key: string\r\n  api_secret?: string\r\n  endpoint?: string\r\n  additional_headers?: Record<string, string>\r\n}\r\n\r\n// Webhook configuration (reverse API)\r\nexport interface WebhookConfig extends BaseAuthConfig {\r\n  auth_method: 'webhook'\r\n  webhook_url: string\r\n  webhook_secret: string\r\n  events: string[]\r\n}\r\n\r\n// Email/Password configuration\r\nexport interface EmailPasswordConfig extends BaseAuthConfig {\r\n  auth_method: 'email_password'\r\n  email: string\r\n  password: string // encrypted\r\n  smtp_server?: string\r\n  smtp_port?: number\r\n}\r\n\r\nexport type WorkflowAuthConfig = OAuthConfig | ApiKeyConfig | WebhookConfig | EmailPasswordConfig\r\n\r\n// Workflow provider definitions\r\nexport interface WorkflowProvider {\r\n  id: string\r\n  name: string\r\n  description: string\r\n  workflow_types: string[]\r\n  auth_method: AuthMethod\r\n  required_scopes?: string[]\r\n  auth_url?: string\r\n  token_url?: string\r\n  client_id?: string\r\n  setup_instructions: string\r\n  icon: string\r\n}\r\n\r\n// ONLY TWO WORKFLOWS SUPPORTED\r\nexport const WORKFLOW_PROVIDERS: Record<string, WorkflowProvider[]> = {\r\n  // 1. SEO Responder - Google review management via AI replies with SEO\r\n  seo_responder: [\r\n    {\r\n      id: 'google_business',\r\n      name: 'Google Business Profile',\r\n      description: 'Connect to manage and respond to Google Business reviews with AI',\r\n      workflow_types: ['seo_responder'],\r\n      auth_method: 'api_key', // Client enters their Google Business API key\r\n      setup_instructions: 'Enter your Google Business API key from Google Cloud Console. We will use AI to generate SEO-optimized responses to your reviews.',\r\n      icon: 'star'\r\n    }\r\n  ],\r\n  \r\n  // 2. Social Poster - Facebook business page posting\r\n  social_poster: [\r\n    {\r\n      id: 'facebook',\r\n      name: 'Facebook Business Pages',\r\n      description: 'Connect your Facebook business page for automated posting',\r\n      workflow_types: ['social_poster'],\r\n      auth_method: 'oauth', // OAuth to OUR Facebook app\r\n      required_scopes: ['pages_manage_posts', 'pages_read_engagement', 'pages_show_list'],\r\n      auth_url: 'https://www.facebook.com/v18.0/dialog/oauth',\r\n      setup_instructions: 'Connect your Facebook business page to automatically post educational content and updates.',\r\n      icon: 'facebook'\r\n    }\r\n  ]\r\n}\r\n\r\nexport interface WorkflowAuthState {\r\n  [workflowType: string]: {\r\n    [providerId: string]: WorkflowAuthConfig | null\r\n  }\r\n}"], "names": [], "mappings": "AAAA,wDAAwD;;;;AAqEjD,MAAM,qBAAyD;IACpE,sEAAsE;IACtE,eAAe;QACb;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,gBAAgB;gBAAC;aAAgB;YACjC,aAAa;YACb,oBAAoB;YACpB,MAAM;QACR;KACD;IAED,oDAAoD;IACpD,eAAe;QACb;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,gBAAgB;gBAAC;aAAgB;YACjC,aAAa;YACb,iBAAiB;gBAAC;gBAAsB;gBAAyB;aAAkB;YACnF,UAAU;YACV,oBAAoB;YACpB,MAAM;QACR;KACD;AACH", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/workflow-auth.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { WORKFLOW_PROVIDERS, type WorkflowAuthConfig } from \"@/lib/types/auth\"\r\nimport { \r\n  Facebook, \r\n  Mail, \r\n  MessageSquare, \r\n  Star, \r\n  Building, \r\n  Key, \r\n  CheckCircle, \r\n  AlertCircle, \r\n  ExternalLink,\r\n  Loader2\r\n} from \"lucide-react\"\r\n\r\ninterface WorkflowAuthProps {\r\n  workflowType: 'review_booster' | 'seo_responder' | 'social_poster'\r\n  authConfigs: WorkflowAuthConfig[]\r\n  onAuthConnect: (provider: string, authMethod: string, data: any) => Promise<void>\r\n  onAuthDisconnect: (configId: string) => Promise<void>\r\n}\r\n\r\nconst getProviderIcon = (providerId: string) => {\r\n  switch (providerId) {\r\n    case 'facebook': return <Facebook className=\"h-5 w-5\" />\r\n    case 'google_business': return <Building className=\"h-5 w-5\" />\r\n    case 'sendgrid': return <Mail className=\"h-5 w-5\" />\r\n    case 'twilio': return <MessageSquare className=\"h-5 w-5\" />\r\n    default: return <Key className=\"h-5 w-5\" />\r\n  }\r\n}\r\n\r\nconst getStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case 'connected': return 'success'\r\n    case 'expired': return 'warning'\r\n    case 'error': return 'destructive'\r\n    case 'pending': return 'secondary'\r\n    default: return 'secondary'\r\n  }\r\n}\r\n\r\nexport function WorkflowAuth({ workflowType, authConfigs, onAuthConnect, onAuthDisconnect }: WorkflowAuthProps) {\r\n  const [loadingProvider, setLoadingProvider] = useState<string | null>(null)\r\n  const [apiKeyInputs, setApiKeyInputs] = useState<Record<string, string>>({})\r\n  \r\n  const providers = WORKFLOW_PROVIDERS[workflowType] || []\r\n  \r\n  const getAuthConfig = (providerId: string) => {\r\n    return authConfigs.find(config => config.provider === providerId)\r\n  }\r\n\r\n  const handleOAuthConnect = async (providerId: string) => {\r\n    setLoadingProvider(providerId)\r\n    try {\r\n      // Initiate OAuth flow\r\n      const provider = providers.find(p => p.id === providerId)\r\n      if (!provider) throw new Error('Provider not found')\r\n      \r\n      // Build OAuth URL\r\n      const params = new URLSearchParams({\r\n        client_id: provider.client_id || process.env.NEXT_PUBLIC_OAUTH_CLIENT_ID || '',\r\n        response_type: 'code',\r\n        scope: provider.required_scopes?.join(' ') || '',\r\n        redirect_uri: `${window.location.origin}/api/auth/callback/${providerId}`,\r\n        state: JSON.stringify({ workflowType, providerId })\r\n      })\r\n      \r\n      const authUrl = `${provider.auth_url}?${params.toString()}`\r\n      \r\n      // Open OAuth popup\r\n      const popup = window.open(authUrl, 'oauth', 'width=600,height=600')\r\n      \r\n      // Listen for completion\r\n      const checkClosed = setInterval(() => {\r\n        if (popup?.closed) {\r\n          clearInterval(checkClosed)\r\n          setLoadingProvider(null)\r\n          // Refresh auth configs\r\n          window.location.reload()\r\n        }\r\n      }, 1000)\r\n      \r\n    } catch (error) {\r\n      console.error('OAuth error:', error)\r\n      setLoadingProvider(null)\r\n    }\r\n  }\r\n\r\n  const handleApiKeyConnect = async (providerId: string) => {\r\n    const apiKey = apiKeyInputs[providerId]\r\n    if (!apiKey) return\r\n\r\n    setLoadingProvider(providerId)\r\n    try {\r\n      await onAuthConnect(providerId, 'api_key', { api_key: apiKey })\r\n      setApiKeyInputs(prev => ({ ...prev, [providerId]: '' }))\r\n    } catch (error) {\r\n      console.error('API key connection error:', error)\r\n    } finally {\r\n      setLoadingProvider(null)\r\n    }\r\n  }\r\n\r\n  const handleDisconnect = async (configId: string) => {\r\n    try {\r\n      await onAuthDisconnect(configId)\r\n    } catch (error) {\r\n      console.error('Disconnect error:', error)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold mb-2\">Connect Your Accounts</h3>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Connect the services you want to automate for this workflow.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"grid gap-4\">\r\n        {providers.map((provider) => {\r\n          const authConfig = getAuthConfig(provider.id)\r\n          const isConnected = authConfig?.status === 'connected'\r\n          const isLoading = loadingProvider === provider.id\r\n\r\n          return (\r\n            <Card key={provider.id} className=\"border-0 bg-card/50 backdrop-blur\">\r\n              <CardHeader className=\"pb-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <div className=\"p-2 rounded-lg bg-secondary/50\">\r\n                      {getProviderIcon(provider.id)}\r\n                    </div>\r\n                    <div>\r\n                      <CardTitle className=\"text-base\">{provider.name}</CardTitle>\r\n                      <CardDescription className=\"text-sm\">\r\n                        {provider.description}\r\n                      </CardDescription>\r\n                    </div>\r\n                  </div>\r\n                  {authConfig && (\r\n                    <Badge variant={getStatusColor(authConfig.status) as any}>\r\n                      {authConfig.status === 'connected' && <CheckCircle className=\"h-3 w-3 mr-1\" />}\r\n                      {authConfig.status === 'error' && <AlertCircle className=\"h-3 w-3 mr-1\" />}\r\n                      {authConfig.status}\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </CardHeader>\r\n\r\n              <CardContent>\r\n                <p className=\"text-sm text-muted-foreground mb-4\">\r\n                  {provider.setup_instructions}\r\n                </p>\r\n\r\n                {isConnected ? (\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-2 text-sm text-green-400\">\r\n                      <CheckCircle className=\"h-4 w-4\" />\r\n                      Connected{authConfig.provider_username && ` as ${authConfig.provider_username}`}\r\n                    </div>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => handleDisconnect(authConfig.id)}\r\n                    >\r\n                      Disconnect\r\n                    </Button>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-3\">\r\n                    {provider.auth_method === 'oauth' ? (\r\n                      <Button\r\n                        onClick={() => handleOAuthConnect(provider.id)}\r\n                        disabled={isLoading}\r\n                        className=\"w-full\"\r\n                        variant=\"outline\"\r\n                      >\r\n                        {isLoading ? (\r\n                          <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                        ) : (\r\n                          <ExternalLink className=\"h-4 w-4 mr-2\" />\r\n                        )}\r\n                        Connect {provider.name}\r\n                      </Button>\r\n                    ) : (\r\n                      <div className=\"flex gap-2\">\r\n                        <Input\r\n                          placeholder=\"Enter API key...\"\r\n                          type=\"password\"\r\n                          value={apiKeyInputs[provider.id] || ''}\r\n                          onChange={(e) => setApiKeyInputs(prev => ({\r\n                            ...prev,\r\n                            [provider.id]: e.target.value\r\n                          }))}\r\n                        />\r\n                        <Button\r\n                          onClick={() => handleApiKeyConnect(provider.id)}\r\n                          disabled={isLoading || !apiKeyInputs[provider.id]}\r\n                        >\r\n                          {isLoading ? (\r\n                            <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                          ) : (\r\n                            'Connect'\r\n                          )}\r\n                        </Button>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {authConfig?.status === 'error' && (\r\n                      <div className=\"text-sm text-destructive\">\r\n                        Connection failed. Please try again.\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          )\r\n        })}\r\n      </div>\r\n\r\n      {providers.length === 0 && (\r\n        <Card className=\"border-0 bg-card/50 backdrop-blur\">\r\n          <CardContent className=\"text-center py-8\">\r\n            <p className=\"text-muted-foreground\">\r\n              No providers configured for this workflow type.\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AA4BA,MAAM,kBAAkB,CAAC;IACvB,OAAQ;QACN,KAAK;YAAY,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5C,KAAK;YAAmB,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QACnD,KAAK;YAAY,qBAAO,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxC,KAAK;YAAU,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAC/C;YAAS,qBAAO,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;IACjC;AACF;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YAAa,OAAO;QACzB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF;AAEO,SAAS,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAqB;IAC5G,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE1E,MAAM,YAAY,2HAAA,CAAA,qBAAkB,CAAC,aAAa,IAAI,EAAE;IAExD,MAAM,gBAAgB,CAAC;QACrB,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IACxD;IAEA,MAAM,qBAAqB,OAAO;QAChC,mBAAmB;QACnB,IAAI;YACF,sBAAsB;YACtB,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC9C,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;YAE/B,kBAAkB;YAClB,MAAM,SAAS,IAAI,gBAAgB;gBACjC,WAAW,SAAS,SAAS,IAAI,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBAC5E,eAAe;gBACf,OAAO,SAAS,eAAe,EAAE,KAAK,QAAQ;gBAC9C,cAAc,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,mBAAmB,EAAE,YAAY;gBACzE,OAAO,KAAK,SAAS,CAAC;oBAAE;oBAAc;gBAAW;YACnD;YAEA,MAAM,UAAU,GAAG,SAAS,QAAQ,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;YAE3D,mBAAmB;YACnB,MAAM,QAAQ,OAAO,IAAI,CAAC,SAAS,SAAS;YAE5C,wBAAwB;YACxB,MAAM,cAAc,YAAY;gBAC9B,IAAI,OAAO,QAAQ;oBACjB,cAAc;oBACd,mBAAmB;oBACnB,uBAAuB;oBACvB,OAAO,QAAQ,CAAC,MAAM;gBACxB;YACF,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,mBAAmB;QACrB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,MAAM,SAAS,YAAY,CAAC,WAAW;QACvC,IAAI,CAAC,QAAQ;QAEb,mBAAmB;QACnB,IAAI;YACF,MAAM,cAAc,YAAY,WAAW;gBAAE,SAAS;YAAO;YAC7D,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,WAAW,EAAE;gBAAG,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,iBAAiB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;oBACd,MAAM,aAAa,cAAc,SAAS,EAAE;oBAC5C,MAAM,cAAc,YAAY,WAAW;oBAC3C,MAAM,YAAY,oBAAoB,SAAS,EAAE;oBAEjD,qBACE,8OAAC,gIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,SAAS,EAAE;;;;;;8DAE9B,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa,SAAS,IAAI;;;;;;sEAC/C,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,SAAS,WAAW;;;;;;;;;;;;;;;;;;wCAI1B,4BACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,eAAe,WAAW,MAAM;;gDAC7C,WAAW,MAAM,KAAK,6BAAe,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAC5D,WAAW,MAAM,KAAK,yBAAW,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACxD,WAAW,MAAM;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAE,WAAU;kDACV,SAAS,kBAAkB;;;;;;oCAG7B,4BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAY;oDACzB,WAAW,iBAAiB,IAAI,CAAC,IAAI,EAAE,WAAW,iBAAiB,EAAE;;;;;;;0DAEjF,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB,WAAW,EAAE;0DAC9C;;;;;;;;;;;6DAKH,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,WAAW,KAAK,wBACxB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,mBAAmB,SAAS,EAAE;gDAC7C,UAAU;gDACV,WAAU;gDACV,SAAQ;;oDAEP,0BACC,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACxB;oDACO,SAAS,IAAI;;;;;;qEAGxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,MAAK;wDACL,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI;wDACpC,UAAU,CAAC,IAAM,gBAAgB,CAAA,OAAQ,CAAC;oEACxC,GAAG,IAAI;oEACP,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;gEAC/B,CAAC;;;;;;kEAEH,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;wDAC9C,UAAU,aAAa,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;kEAEhD,0BACC,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;mEAEnB;;;;;;;;;;;;4CAMP,YAAY,WAAW,yBACtB,8OAAC;gDAAI,WAAU;0DAA2B;;;;;;;;;;;;;;;;;;;uBApFzC,SAAS,EAAE;;;;;gBA6F1B;;;;;;YAGD,UAAU,MAAM,KAAK,mBACpB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\r\nimport { Database } from './types'\r\n\r\nexport function createClient() {\r\n  return createBrowserClient<Database>(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  )\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { WorkflowAuth } from \"@/components/workflow-auth\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { Database } from \"@/lib/supabase/types\"\nimport { WorkflowAuthConfig } from \"@/lib/types/auth\"\nimport { \n  Star, \n  MessageSquare, \n  Share2, \n  Clock, \n  Settings, \n  Play, \n  Pause, \n  BarChart3,\n  Calendar,\n  CheckCircle,\n  AlertCircle,\n  Zap,\n  Loader2\n} from \"lucide-react\"\n\ntype AutomationConfig = Database['public']['Tables']['automation_configs']['Row']\ntype DatabaseAuthConfig = Database['public']['Tables']['workflow_auth_configs']['Row']\ntype LogEntry = Database['public']['Tables']['logs']['Row']\n\n// Helper function to convert database auth config to component format\nconst convertAuthConfig = (dbConfig: DatabaseAuthConfig): WorkflowAuthConfig => {\n  return {\n    id: dbConfig.id,\n    user_id: dbConfig.user_id,\n    workflow_type: dbConfig.workflow_type,\n    provider: dbConfig.provider,\n    auth_method: dbConfig.auth_method,\n    status: dbConfig.status,\n    created_at: dbConfig.created_at,\n    updated_at: dbConfig.updated_at,\n    expires_at: dbConfig.expires_at,\n    provider_user_id: dbConfig.provider_user_id,\n    provider_username: dbConfig.provider_username,\n    ...dbConfig.auth_data\n  } as WorkflowAuthConfig\n}\n\ninterface DashboardStats {\n  activeAutomations: number\n  totalAutomations: number\n  reviewsThisWeek: number\n  responsesSent: number\n  postsPublished: number\n}\n\nconst automationDetails = {\n  review_booster: {\n    title: 'Review Booster',\n    description: 'Automatically request reviews from satisfied customers',\n    icon: <Star className=\"h-5 w-5\" />,\n    color: 'text-green-400'\n  },\n  seo_responder: {\n    title: 'SEO Responder',\n    description: 'AI-powered responses to Google Business reviews',\n    icon: <MessageSquare className=\"h-5 w-5\" />,\n    color: 'text-blue-400'\n  },\n  social_poster: {\n    title: 'Social Poster',\n    description: 'Automated educational content posting',\n    icon: <Share2 className=\"h-5 w-5\" />,\n    color: 'text-purple-400'\n  }\n}\n\nexport default function Dashboard() {\n  const [selectedAutomation, setSelectedAutomation] = useState<string | null>(null)\n  const [automations, setAutomations] = useState<AutomationConfig[]>([])\n  const [authConfigs, setAuthConfigs] = useState<DatabaseAuthConfig[]>([])\n  const [recentLogs, setRecentLogs] = useState<LogEntry[]>([])\n  const [stats, setStats] = useState<DashboardStats>({\n    activeAutomations: 0,\n    totalAutomations: 0,\n    reviewsThisWeek: 0,\n    responsesSent: 0,\n    postsPublished: 0\n  })\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  const supabase = createClient()\n\n  useEffect(() => {\n    loadDashboardData()\n  }, [])\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      // Get current user\n      const { data: { user }, error: userError } = await supabase.auth.getUser()\n      if (userError || !user) {\n        setError('Please log in to view your dashboard')\n        return\n      }\n\n      // Fetch automations, auth configs, and logs in parallel\n      const [automationsRes, authConfigsRes, logsRes] = await Promise.all([\n        supabase\n          .from('automation_configs')\n          .select('*')\n          .eq('user_id', user.id)\n          .order('created_at', { ascending: false }),\n        \n        supabase\n          .from('workflow_auth_configs')\n          .select('*')\n          .eq('user_id', user.id)\n          .eq('status', 'connected'),\n        \n        supabase\n          .from('logs')\n          .select('*')\n          .eq('user_id', user.id)\n          .order('timestamp', { ascending: false })\n          .limit(10)\n      ])\n\n      if (automationsRes.error) throw automationsRes.error\n      if (authConfigsRes.error) throw authConfigsRes.error\n      if (logsRes.error) throw logsRes.error\n\n      setAutomations(automationsRes.data || [])\n      setAuthConfigs(authConfigsRes.data || [])\n      setRecentLogs(logsRes.data || [])\n\n      // Calculate stats\n      await calculateStats(user.id, automationsRes.data || [])\n      \n    } catch (err) {\n      console.error('Error loading dashboard:', err)\n      setError('Failed to load dashboard data')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateStats = async (userId: string, automationConfigs: AutomationConfig[]) => {\n    try {\n      const activeAutomations = automationConfigs.filter(a => a.is_active).length\n      const totalAutomations = automationConfigs.length\n\n      // Calculate date ranges\n      const oneWeekAgo = new Date()\n      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)\n\n      const oneMonthAgo = new Date()\n      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)\n\n      // Fetch analytics data in parallel\n      const [reviewLogsRes, responseLogsRes, postLogsRes] = await Promise.all([\n        supabase\n          .from('logs')\n          .select('*')\n          .eq('user_id', userId)\n          .eq('automation_type', 'review_booster')\n          .eq('event_type', 'success')\n          .gte('timestamp', oneWeekAgo.toISOString()),\n        \n        supabase\n          .from('logs')\n          .select('*')\n          .eq('user_id', userId)\n          .eq('automation_type', 'seo_responder')\n          .eq('event_type', 'success')\n          .gte('timestamp', oneWeekAgo.toISOString()),\n        \n        supabase\n          .from('logs')\n          .select('*')\n          .eq('user_id', userId)\n          .eq('automation_type', 'social_poster')\n          .eq('event_type', 'success')\n          .gte('timestamp', oneMonthAgo.toISOString())\n      ])\n\n      setStats({\n        activeAutomations,\n        totalAutomations,\n        reviewsThisWeek: reviewLogsRes.data?.length || 0,\n        responsesSent: responseLogsRes.data?.length || 0,\n        postsPublished: postLogsRes.data?.length || 0\n      })\n    } catch (err) {\n      console.error('Error calculating stats:', err)\n    }\n  }\n\n  const handleToggleAutomation = async (automationId: string) => {\n    try {\n      const automation = automations.find(a => a.id === automationId)\n      if (!automation) return\n\n      const newStatus = !automation.is_active\n      \n      // Update via API endpoint\n      const response = await fetch('/api/automation-config', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          automationId,\n          isActive: newStatus\n        })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to update automation')\n      }\n\n      // Update local state\n      setAutomations(prev => prev.map(a => \n        a.id === automationId \n          ? { ...a, is_active: newStatus }\n          : a\n      ))\n\n      // Recalculate stats\n      const { data: { user } } = await supabase.auth.getUser()\n      if (user) {\n        const updatedAutomations = automations.map(a => \n          a.id === automationId ? { ...a, is_active: newStatus } : a\n        )\n        await calculateStats(user.id, updatedAutomations)\n      }\n      \n    } catch (err) {\n      console.error('Error toggling automation:', err)\n      setError('Failed to update automation')\n    }\n  }\n\n  const handleAuthConnect = async (provider: string, authMethod: string, data: any) => {\n    try {\n      const response = await fetch('/api/workflow-auth', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          workflowType: selectedAutomation ? automations.find(a => a.id === selectedAutomation)?.automation_type : 'review_booster',\n          provider,\n          authMethod,\n          authData: data\n        })\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to connect authentication')\n      }\n\n      // Reload auth configs\n      await loadDashboardData()\n      \n    } catch (err) {\n      console.error('Error connecting auth:', err)\n      setError('Failed to connect authentication')\n    }\n  }\n\n  const handleAuthDisconnect = async (configId: string) => {\n    try {\n      await supabase\n        .from('workflow_auth_configs')\n        .delete()\n        .eq('id', configId)\n\n      // Reload auth configs\n      await loadDashboardData()\n      \n    } catch (err) {\n      console.error('Error disconnecting auth:', err)\n      setError('Failed to disconnect authentication')\n    }\n  }\n\n  const formatCronSchedule = (cron: string) => {\n    const scheduleMap: Record<string, string> = {\n      '0 9 * * *': 'Daily at 9:00 AM',\n      '0 */4 * * *': 'Every 4 hours',\n      '0 10 * * 1': 'Weekly on Monday at 10:00 AM',\n      '0 0 * * 0': 'Weekly on Sunday at midnight'\n    }\n    return scheduleMap[cron] || cron\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <div className=\"container mx-auto py-8 px-4 max-w-7xl\">\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"flex items-center gap-3\">\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n              <span className=\"text-lg\">Loading your dashboard...</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <div className=\"container mx-auto py-8 px-4 max-w-7xl\">\n          <div className=\"flex items-center justify-center h-64\">\n            <Card className=\"border-0 bg-card/50 backdrop-blur max-w-md\">\n              <CardContent className=\"text-center py-8\">\n                <AlertCircle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Error Loading Dashboard</h3>\n                <p className=\"text-muted-foreground mb-4\">{error}</p>\n                <Button onClick={loadDashboardData} variant=\"outline\">\n                  Try Again\n                </Button>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <div className=\"container mx-auto py-8 px-4 max-w-7xl\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold mb-2\">Dashboard</h1>\n          <p className=\"text-muted-foreground\">\n            Manage your marketing automations and view performance analytics.\n          </p>\n        </div>\n\n        {/* Stats Overview */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <Card className=\"border-0 bg-card/50 backdrop-blur\">\n            <CardHeader className=\"pb-3\">\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-sm font-medium\">Active Automations</CardTitle>\n                <Zap className=\"h-4 w-4 text-green-400\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.activeAutomations}</div>\n              <p className=\"text-xs text-muted-foreground\">out of {stats.totalAutomations} workflows</p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-0 bg-card/50 backdrop-blur\">\n            <CardHeader className=\"pb-3\">\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-sm font-medium\">Reviews This Week</CardTitle>\n                <Star className=\"h-4 w-4 text-yellow-400\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.reviewsThisWeek}</div>\n              <p className=\"text-xs text-muted-foreground\">automated requests</p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-0 bg-card/50 backdrop-blur\">\n            <CardHeader className=\"pb-3\">\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-sm font-medium\">Responses Sent</CardTitle>\n                <MessageSquare className=\"h-4 w-4 text-blue-400\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.responsesSent}</div>\n              <p className=\"text-xs text-muted-foreground\">this week</p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-0 bg-card/50 backdrop-blur\">\n            <CardHeader className=\"pb-3\">\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-sm font-medium\">Posts Published</CardTitle>\n                <Share2 className=\"h-4 w-4 text-purple-400\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.postsPublished}</div>\n              <p className=\"text-xs text-muted-foreground\">this month</p>\n            </CardContent>\n          </Card>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Automations List */}\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-xl font-semibold\">Your Automations</h2>\n              <Button variant=\"outline\" size=\"sm\">\n                <Settings className=\"h-4 w-4 mr-2\" />\n                Manage All\n              </Button>\n            </div>\n\n            <div className=\"space-y-4\">\n              {automations.length > 0 ? (\n                automations.map((automation) => {\n                  const details = automationDetails[automation.automation_type]\n                  const isSelected = selectedAutomation === automation.id\n\n                  return (\n                    <Card \n                      key={automation.id} \n                      className={`border-0 bg-card/50 backdrop-blur transition-all duration-200 cursor-pointer hover:shadow-lg ${\n                        isSelected ? 'ring-2 ring-brand-600' : ''\n                      }`}\n                      onClick={() => setSelectedAutomation(isSelected ? null : automation.id)}\n                    >\n                    <CardHeader className=\"pb-4\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className={`p-2 rounded-lg bg-secondary/50 ${details.color}`}>\n                            {details.icon}\n                          </div>\n                          <div>\n                            <CardTitle className=\"text-base\">{details.title}</CardTitle>\n                            <CardDescription className=\"text-sm\">\n                              {details.description}\n                            </CardDescription>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <Badge variant={automation.is_active ? 'success' : 'secondary'}>\n                            {automation.is_active ? 'Active' : 'Paused'}\n                          </Badge>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleToggleAutomation(automation.id)\n                            }}\n                          >\n                            {automation.is_active ? (\n                              <Pause className=\"h-4 w-4\" />\n                            ) : (\n                              <Play className=\"h-4 w-4\" />\n                            )}\n                          </Button>\n                        </div>\n                      </div>\n                    </CardHeader>\n\n                    <CardContent>\n                      <div className=\"space-y-3\">\n                        <div className=\"flex items-center justify-between text-sm\">\n                          <span className=\"text-muted-foreground\">Schedule:</span>\n                          <span>{formatCronSchedule(automation.cron_schedule)}</span>\n                        </div>\n                        \n                        {automation.last_run && (\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <span className=\"text-muted-foreground\">Last run:</span>\n                            <span>{new Date(automation.last_run).toLocaleDateString()}</span>\n                          </div>\n                        )}\n                        \n                        {automation.next_run && automation.is_active && (\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <span className=\"text-muted-foreground\">Next run:</span>\n                            <span>{new Date(automation.next_run).toLocaleDateString()}</span>\n                          </div>\n                        )}\n\n                        {automation.workflow_id ? (\n                          <div className=\"flex items-center gap-2 text-sm text-green-400\">\n                            <CheckCircle className=\"h-3 w-3\" />\n                            Workflow configured\n                          </div>\n                        ) : (\n                          <div className=\"flex items-center gap-2 text-sm text-yellow-400\">\n                            <AlertCircle className=\"h-3 w-3\" />\n                            Setup required\n                          </div>\n                        )}\n                      </div>\n                    </CardContent>\n                  </Card>\n                  )\n                })\n              ) : (\n                <Card className=\"border-0 bg-card/50 backdrop-blur\">\n                  <CardContent className=\"text-center py-12\">\n                    <Zap className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold mb-2\">No Automations Yet</h3>\n                    <p className=\"text-muted-foreground mb-4\">\n                      Create your first automation to start growing your business.\n                    </p>\n                    <Button variant=\"outline\">\n                      <Settings className=\"h-4 w-4 mr-2\" />\n                      Create Automation\n                    </Button>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n          </div>\n\n          {/* Authentication Setup */}\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-xl font-semibold\">Account Connections</h2>\n              <Button variant=\"outline\" size=\"sm\">\n                <BarChart3 className=\"h-4 w-4 mr-2\" />\n                View Analytics\n              </Button>\n            </div>\n\n            {selectedAutomation ? (\n              <WorkflowAuth\n                workflowType={automations.find(a => a.id === selectedAutomation)?.automation_type || 'review_booster'}\n                authConfigs={authConfigs\n                  .filter(config => \n                    config.workflow_type === automations.find(a => a.id === selectedAutomation)?.automation_type\n                  )\n                  .map(convertAuthConfig)\n                }\n                onAuthConnect={handleAuthConnect}\n                onAuthDisconnect={handleAuthDisconnect}\n              />\n            ) : (\n              <Card className=\"border-0 bg-card/50 backdrop-blur\">\n                <CardContent className=\"text-center py-12\">\n                  <Settings className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">Select an Automation</h3>\n                  <p className=\"text-muted-foreground mb-4\">\n                    Choose an automation from the left to configure its account connections and settings.\n                  </p>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"mt-8\">\n          <h2 className=\"text-xl font-semibold mb-4\">Recent Activity</h2>\n          <Card className=\"border-0 bg-card/50 backdrop-blur\">\n            <CardContent className=\"p-6\">\n              {recentLogs.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {recentLogs.map((log) => {\n                    const details = automationDetails[log.automation_type]\n                    const timeAgo = new Date(log.timestamp).toLocaleString()\n                    \n                    return (\n                      <div key={log.id} className=\"flex items-center gap-4 p-3 rounded-lg bg-secondary/20\">\n                        <div className={`p-2 rounded-full bg-${log.event_type === 'success' ? 'green' : log.event_type === 'error' ? 'red' : 'yellow'}-600/10`}>\n                          {details.icon}\n                        </div>\n                        <div className=\"flex-1\">\n                          <p className=\"text-sm font-medium\">{log.message}</p>\n                          <p className=\"text-xs text-muted-foreground\">\n                            {details.title} • {timeAgo}\n                          </p>\n                        </div>\n                        <Badge \n                          variant={log.event_type === 'success' ? 'success' : \n                                  log.event_type === 'error' ? 'destructive' : 'secondary'}\n                        >\n                          {log.event_type}\n                        </Badge>\n                      </div>\n                    )\n                  })}\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <Clock className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No Recent Activity</h3>\n                  <p className=\"text-muted-foreground\">\n                    Automation activity will appear here once your workflows start running.\n                  </p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;AA8BA,sEAAsE;AACtE,MAAM,oBAAoB,CAAC;IACzB,OAAO;QACL,IAAI,SAAS,EAAE;QACf,SAAS,SAAS,OAAO;QACzB,eAAe,SAAS,aAAa;QACrC,UAAU,SAAS,QAAQ;QAC3B,aAAa,SAAS,WAAW;QACjC,QAAQ,SAAS,MAAM;QACvB,YAAY,SAAS,UAAU;QAC/B,YAAY,SAAS,UAAU;QAC/B,YAAY,SAAS,UAAU;QAC/B,kBAAkB,SAAS,gBAAgB;QAC3C,mBAAmB,SAAS,iBAAiB;QAC7C,GAAG,SAAS,SAAS;IACvB;AACF;AAUA,MAAM,oBAAoB;IACxB,gBAAgB;QACd,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,OAAO;IACT;IACA,eAAe;QACb,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/B,OAAO;IACT;IACA,eAAe;QACb,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,0MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,OAAO;IACT;AACF;AAEe,SAAS;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,SAAS;YAET,mBAAmB;YACnB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACxE,IAAI,aAAa,CAAC,MAAM;gBACtB,SAAS;gBACT;YACF;YAEA,wDAAwD;YACxD,MAAM,CAAC,gBAAgB,gBAAgB,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClE,SACG,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,SACG,IAAI,CAAC,yBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,EAAE,CAAC,UAAU;gBAEhB,SACG,IAAI,CAAC,QACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,aAAa;oBAAE,WAAW;gBAAM,GACtC,KAAK,CAAC;aACV;YAED,IAAI,eAAe,KAAK,EAAE,MAAM,eAAe,KAAK;YACpD,IAAI,eAAe,KAAK,EAAE,MAAM,eAAe,KAAK;YACpD,IAAI,QAAQ,KAAK,EAAE,MAAM,QAAQ,KAAK;YAEtC,eAAe,eAAe,IAAI,IAAI,EAAE;YACxC,eAAe,eAAe,IAAI,IAAI,EAAE;YACxC,cAAc,QAAQ,IAAI,IAAI,EAAE;YAEhC,kBAAkB;YAClB,MAAM,eAAe,KAAK,EAAE,EAAE,eAAe,IAAI,IAAI,EAAE;QAEzD,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO,QAAgB;QAC5C,IAAI;YACF,MAAM,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;YAC3E,MAAM,mBAAmB,kBAAkB,MAAM;YAEjD,wBAAwB;YACxB,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,MAAM,cAAc,IAAI;YACxB,YAAY,QAAQ,CAAC,YAAY,QAAQ,KAAK;YAE9C,mCAAmC;YACnC,MAAM,CAAC,eAAe,iBAAiB,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtE,SACG,IAAI,CAAC,QACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,mBAAmB,kBACtB,EAAE,CAAC,cAAc,WACjB,GAAG,CAAC,aAAa,WAAW,WAAW;gBAE1C,SACG,IAAI,CAAC,QACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,mBAAmB,iBACtB,EAAE,CAAC,cAAc,WACjB,GAAG,CAAC,aAAa,WAAW,WAAW;gBAE1C,SACG,IAAI,CAAC,QACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,mBAAmB,iBACtB,EAAE,CAAC,cAAc,WACjB,GAAG,CAAC,aAAa,YAAY,WAAW;aAC5C;YAED,SAAS;gBACP;gBACA;gBACA,iBAAiB,cAAc,IAAI,EAAE,UAAU;gBAC/C,eAAe,gBAAgB,IAAI,EAAE,UAAU;gBAC/C,gBAAgB,YAAY,IAAI,EAAE,UAAU;YAC9C;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAClD,IAAI,CAAC,YAAY;YAEjB,MAAM,YAAY,CAAC,WAAW,SAAS;YAEvC,0BAA0B;YAC1B,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,UAAU;gBACZ;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,qBAAqB;YACrB,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC9B,EAAE,EAAE,KAAK,eACL;wBAAE,GAAG,CAAC;wBAAE,WAAW;oBAAU,IAC7B;YAGN,oBAAoB;YACpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,MAAM;gBACR,MAAM,qBAAqB,YAAY,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,eAAe;wBAAE,GAAG,CAAC;wBAAE,WAAW;oBAAU,IAAI;gBAE3D,MAAM,eAAe,KAAK,EAAE,EAAE;YAChC;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB,OAAO,UAAkB,YAAoB;QACrE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,cAAc,qBAAqB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,qBAAqB,kBAAkB;oBACzG;oBACA;oBACA,UAAU;gBACZ;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,sBAAsB;YACtB,MAAM;QAER,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,SACH,IAAI,CAAC,yBACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,sBAAsB;YACtB,MAAM;QAER,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAsC;YAC1C,aAAa;YACb,eAAe;YACf,cAAc;YACd,aAAa;QACf;QACA,OAAO,WAAW,CAAC,KAAK,IAAI;IAC9B;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMtC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAC3C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAmB,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASpE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGnB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,MAAM,iBAAiB;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;;gDAAgC;gDAAQ,MAAM,gBAAgB;gDAAC;;;;;;;;;;;;;;;;;;;sCAIhF,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGpB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,MAAM,eAAe;;;;;;sDAC1D,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAIjD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG7B,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAIjD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGtB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsB,MAAM,cAAc;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAKnD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAKzC,8OAAC;oCAAI,WAAU;8CACZ,YAAY,MAAM,GAAG,IACpB,YAAY,GAAG,CAAC,CAAC;wCACf,MAAM,UAAU,iBAAiB,CAAC,WAAW,eAAe,CAAC;wCAC7D,MAAM,aAAa,uBAAuB,WAAW,EAAE;wCAEvD,qBACE,8OAAC,gIAAA,CAAA,OAAI;4CAEH,WAAW,CAAC,6FAA6F,EACvG,aAAa,0BAA0B,IACvC;4CACF,SAAS,IAAM,sBAAsB,aAAa,OAAO,WAAW,EAAE;;8DAExE,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAW,CAAC,+BAA+B,EAAE,QAAQ,KAAK,EAAE;kFAC9D,QAAQ,IAAI;;;;;;kFAEf,8OAAC;;0FACC,8OAAC,gIAAA,CAAA,YAAS;gFAAC,WAAU;0FAAa,QAAQ,KAAK;;;;;;0FAC/C,8OAAC,gIAAA,CAAA,kBAAe;gFAAC,WAAU;0FACxB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;0EAI1B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAS,WAAW,SAAS,GAAG,YAAY;kFAChD,WAAW,SAAS,GAAG,WAAW;;;;;;kFAErC,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,uBAAuB,WAAW,EAAE;wEACtC;kFAEC,WAAW,SAAS,iBACnB,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;iGAEjB,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAO1B,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;kFAAM,mBAAmB,WAAW,aAAa;;;;;;;;;;;;4DAGnD,WAAW,QAAQ,kBAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;kFAAM,IAAI,KAAK,WAAW,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;4DAI1D,WAAW,QAAQ,IAAI,WAAW,SAAS,kBAC1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;kFAAM,IAAI,KAAK,WAAW,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;4DAI1D,WAAW,WAAW,iBACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAY;;;;;;qFAIrC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;;;;;;;2CArEpC,WAAW,EAAE;;;;;oCA6ExB,mBAEA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAKzC,mCACC,8OAAC,sIAAA,CAAA,eAAY;oCACX,cAAc,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,qBAAqB,mBAAmB;oCACrF,aAAa,YACV,MAAM,CAAC,CAAA,SACN,OAAO,aAAa,KAAK,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,qBAAqB,iBAE9E,GAAG,CAAC;oCAEP,eAAe;oCACf,kBAAkB;;;;;yDAGpB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,WAAW,MAAM,GAAG,kBACnB,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,UAAU,iBAAiB,CAAC,IAAI,eAAe,CAAC;wCACtD,MAAM,UAAU,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;wCAEtD,qBACE,8OAAC;4CAAiB,WAAU;;8DAC1B,8OAAC;oDAAI,WAAW,CAAC,oBAAoB,EAAE,IAAI,UAAU,KAAK,YAAY,UAAU,IAAI,UAAU,KAAK,UAAU,QAAQ,SAAS,OAAO,CAAC;8DACnI,QAAQ,IAAI;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAuB,IAAI,OAAO;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;;gEACV,QAAQ,KAAK;gEAAC;gEAAI;;;;;;;;;;;;;8DAGvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAS,IAAI,UAAU,KAAK,YAAY,YAChC,IAAI,UAAU,KAAK,UAAU,gBAAgB;8DAEpD,IAAI,UAAU;;;;;;;2CAdT,IAAI,EAAE;;;;;oCAkBpB;;;;;yDAGF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}]}