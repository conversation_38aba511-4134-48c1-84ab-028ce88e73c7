"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { WORKFLOW_PROVIDERS, type WorkflowAuthConfig } from "@/lib/types/auth"
import { 
  Facebook, 
  Mail, 
  MessageSquare, 
  Star, 
  Building, 
  Key, 
  CheckCircle, 
  AlertCircle, 
  ExternalLink,
  Loader2
} from "lucide-react"

interface WorkflowAuthProps {
  workflowType: 'review_booster' | 'seo_responder' | 'social_poster'
  authConfigs: WorkflowAuthConfig[]
  onAuthConnect: (provider: string, authMethod: string, data: any) => Promise<void>
  onAuthDisconnect: (configId: string) => Promise<void>
}

const getProviderIcon = (providerId: string) => {
  switch (providerId) {
    case 'facebook': return <Facebook className="h-5 w-5" />
    case 'google_business': return <Building className="h-5 w-5" />
    case 'sendgrid': return <Mail className="h-5 w-5" />
    case 'twilio': return <MessageSquare className="h-5 w-5" />
    default: return <Key className="h-5 w-5" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'connected': return 'success'
    case 'expired': return 'warning'
    case 'error': return 'destructive'
    case 'pending': return 'secondary'
    default: return 'secondary'
  }
}

export function WorkflowAuth({ workflowType, authConfigs, onAuthConnect, onAuthDisconnect }: WorkflowAuthProps) {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null)
  const [apiKeyInputs, setApiKeyInputs] = useState<Record<string, string>>({})
  
  const providers = WORKFLOW_PROVIDERS[workflowType] || []
  
  const getAuthConfig = (providerId: string) => {
    return authConfigs.find(config => config.provider === providerId)
  }

  const handleOAuthConnect = async (providerId: string) => {
    setLoadingProvider(providerId)
    try {
      // Initiate OAuth flow
      const provider = providers.find(p => p.id === providerId)
      if (!provider) throw new Error('Provider not found')
      
      // Build OAuth URL
      const params = new URLSearchParams({
        client_id: provider.client_id || process.env.NEXT_PUBLIC_OAUTH_CLIENT_ID || '',
        response_type: 'code',
        scope: provider.required_scopes?.join(' ') || '',
        redirect_uri: `${window.location.origin}/api/auth/callback/${providerId}`,
        state: JSON.stringify({ workflowType, providerId })
      })
      
      const authUrl = `${provider.auth_url}?${params.toString()}`
      
      // Open OAuth popup
      const popup = window.open(authUrl, 'oauth', 'width=600,height=600')
      
      // Listen for completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed)
          setLoadingProvider(null)
          // Refresh auth configs
          window.location.reload()
        }
      }, 1000)
      
    } catch (error) {
      console.error('OAuth error:', error)
      setLoadingProvider(null)
    }
  }

  const handleApiKeyConnect = async (providerId: string) => {
    const apiKey = apiKeyInputs[providerId]
    if (!apiKey) return

    setLoadingProvider(providerId)
    try {
      await onAuthConnect(providerId, 'api_key', { api_key: apiKey })
      setApiKeyInputs(prev => ({ ...prev, [providerId]: '' }))
    } catch (error) {
      console.error('API key connection error:', error)
    } finally {
      setLoadingProvider(null)
    }
  }

  const handleDisconnect = async (configId: string) => {
    try {
      await onAuthDisconnect(configId)
    } catch (error) {
      console.error('Disconnect error:', error)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Connect Your Accounts</h3>
        <p className="text-sm text-muted-foreground">
          Connect the services you want to automate for this workflow.
        </p>
      </div>

      <div className="grid gap-4">
        {providers.map((provider) => {
          const authConfig = getAuthConfig(provider.id)
          const isConnected = authConfig?.status === 'connected'
          const isLoading = loadingProvider === provider.id

          return (
            <Card key={provider.id} className="border-0 bg-card/50 backdrop-blur">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-secondary/50">
                      {getProviderIcon(provider.id)}
                    </div>
                    <div>
                      <CardTitle className="text-base">{provider.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {provider.description}
                      </CardDescription>
                    </div>
                  </div>
                  {authConfig && (
                    <Badge variant={getStatusColor(authConfig.status) as any}>
                      {authConfig.status === 'connected' && <CheckCircle className="h-3 w-3 mr-1" />}
                      {authConfig.status === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
                      {authConfig.status}
                    </Badge>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {provider.setup_instructions}
                </p>

                {isConnected ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-green-400">
                      <CheckCircle className="h-4 w-4" />
                      Connected{authConfig.provider_username && ` as ${authConfig.provider_username}`}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect(authConfig.id)}
                    >
                      Disconnect
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {provider.auth_method === 'oauth' ? (
                      <Button
                        onClick={() => handleOAuthConnect(provider.id)}
                        disabled={isLoading}
                        className="w-full"
                        variant="outline"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <ExternalLink className="h-4 w-4 mr-2" />
                        )}
                        Connect {provider.name}
                      </Button>
                    ) : (
                      <div className="flex gap-2">
                        <Input
                          placeholder="Enter API key..."
                          type="password"
                          value={apiKeyInputs[provider.id] || ''}
                          onChange={(e) => setApiKeyInputs(prev => ({
                            ...prev,
                            [provider.id]: e.target.value
                          }))}
                        />
                        <Button
                          onClick={() => handleApiKeyConnect(provider.id)}
                          disabled={isLoading || !apiKeyInputs[provider.id]}
                        >
                          {isLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            'Connect'
                          )}
                        </Button>
                      </div>
                    )}
                    
                    {authConfig?.status === 'error' && (
                      <div className="text-sm text-destructive">
                        Connection failed. Please try again.
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {providers.length === 0 && (
        <Card className="border-0 bg-card/50 backdrop-blur">
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">
              No providers configured for this workflow type.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}