import Stripe from 'stripe'

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
})

export const getStripeCustomer = async (customerId: string) => {
  try {
    const customer = await stripe.customers.retrieve(customerId)
    return customer
  } catch (error) {
    console.error('Error fetching Stripe customer:', error)
    return null
  }
}

export const createStripeCustomer = async (email: string, name?: string) => {
  try {
    const customer = await stripe.customers.create({
      email,
      name,
    })
    return customer
  } catch (error) {
    console.error('Error creating Stripe customer:', error)
    throw error
  }
}

export const createCheckoutSession = async ({
  customerId,
  priceId,
  successUrl,
  cancelUrl,
}: {
  customerId: string
  priceId: string
  successUrl: string
  cancelUrl: string
}) => {
  try {
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      billing_address_collection: 'required',
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      allow_promotion_codes: true,
    })
    return session
  } catch (error) {
    console.error('Error creating checkout session:', error)
    throw error
  }
}

export const createBillingPortalSession = async ({
  customerId,
  returnUrl,
}: {
  customerId: string
  returnUrl: string
}) => {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    })
    return session
  } catch (error) {
    console.error('Error creating billing portal session:', error)
    throw error
  }
}

export const constructEvent = (body: string, signature: string) => {
  return stripe.webhooks.constructEvent(
    body,
    signature,
    process.env.STRIPE_WEBHOOK_SECRET!
  )
}

// Price IDs for different plans
export const PRICE_IDS = {
  basic: process.env.STRIPE_BASIC_PRICE_ID!,
  standard: process.env.STRIPE_STANDARD_PRICE_ID!,
  premium: process.env.STRIPE_PREMIUM_PRICE_ID!,
}

export const PLAN_DETAILS = {
  basic: {
    name: 'Basic',
    price: 49,
    automations: 1,
    features: [
      '1 Automation',
      'Basic Analytics',
      'Email Support',
      '500 Review Requests/month',
    ],
  },
  standard: {
    name: 'Standard',
    price: 99,
    automations: 3,
    features: [
      '3 Automations',
      'Advanced Analytics',
      'Priority Support',
      '2,000 Review Requests/month',
      'SEO Auto-Replies',
      'Social Media Posting',
    ],
  },
  premium: {
    name: 'Premium',
    price: 199,
    automations: -1, // unlimited
    features: [
      'Unlimited Automations',
      'Full Analytics Suite',
      '24/7 Support',
      'Unlimited Review Requests',
      'AI-Enhanced Content',
      'Custom Workflows',
      'White-label Options',
    ],
  },
}