"use client"

import Link from "next/link"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, User, Settings, LogOut } from "lucide-react"
import { cn } from "@/lib/utils"

interface NavigationProps {
  user?: {
    id: string
    email: string
    full_name?: string
  } | null
}

export function Navigation({ user }: NavigationProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isOpen) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [isOpen])

  return (
    <>
      {/* Modern Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50">
        <div 
          className="flex items-center gap-3 px-6 py-3 bg-slate-900/80 backdrop-blur-xl border border-slate-700/50 rounded-full shadow-2xl shadow-slate-900/50"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Logo */}
          <Link href="/" className="flex items-center group">
            <div className="text-left">
              <div className="text-lg font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                Proof Engine
              </div>
            </div>
          </Link>

          {/* Navigation Pills */}
          <div className="hidden md:flex items-center gap-2 ml-6">
            <Link
              href="/features"
              className="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-full transition-all duration-300"
            >
              Features
            </Link>
            <Link
              href="/pricing"
              className="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-full transition-all duration-300"
            >
              Pricing
            </Link>
            <Link
              href="/blog"
              className="px-4 py-2 text-sm font-medium text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-full transition-all duration-300"
            >
              Blog
            </Link>
          </div>

          {/* Auth Buttons */}
          <div className="hidden md:flex items-center gap-2 ml-6">
            {user ? (
              <>
                <Link href="/dashboard">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-full transition-all duration-300"
                  >
                    <User className="h-4 w-4 mr-2" />
                    Dashboard
                  </Button>
                </Link>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-full transition-all duration-300"
                >
                  <Settings className="h-4 w-4" />
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-full transition-all duration-300"
                >
                  <LogOut className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <>
                <Link href="/login">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-full transition-all duration-300 px-4"
                  >
                    Sign In
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button 
                    size="sm" 
                    className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white border-0 shadow-lg shadow-emerald-600/25 transition-all duration-300 hover:scale-105 hover:shadow-emerald-500/30 rounded-full px-4"
                  >
                    Get Started
                  </Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden ml-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(!isOpen)}
              className="rounded-full"
            >
              {isOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </nav>

      {/* Modern Mobile menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div className="md:hidden fixed inset-0 bg-slate-900/20 backdrop-blur-sm z-30" />
          
          <div className="md:hidden fixed top-20 left-1/2 transform -translate-x-1/2 z-40">
            <div 
              className="bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-2xl shadow-2xl shadow-slate-900/50 p-6 min-w-[280px]"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="space-y-3">
                <Link
                  href="/features"
                  className="block px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-xl transition-all duration-300 font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  Features
                </Link>
                <Link
                  href="/pricing"
                  className="block px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-xl transition-all duration-300 font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  Pricing
                </Link>
                <Link
                  href="/blog"
                  className="block px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-xl transition-all duration-300 font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  Blog
                </Link>
                
                <div className="border-t border-slate-700/50 pt-3 mt-3">
                  {user ? (
                    <div className="space-y-2">
                      <Link
                        href="/dashboard"
                        className="block px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-xl transition-all duration-300 font-medium"
                        onClick={() => setIsOpen(false)}
                      >
                        <User className="h-4 w-4 inline mr-2" />
                        Dashboard
                      </Link>
                      <Link
                        href="/dashboard/settings"
                        className="block px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-xl transition-all duration-300 font-medium"
                        onClick={() => setIsOpen(false)}
                      >
                        <Settings className="h-4 w-4 inline mr-2" />
                        Settings
                      </Link>
                      <button 
                        className="w-full text-left px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-xl transition-all duration-300 font-medium"
                        onClick={() => setIsOpen(false)}
                      >
                        <LogOut className="h-4 w-4 inline mr-2" />
                        Sign Out
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Link
                        href="/login"
                        className="block px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/60 rounded-xl transition-all duration-300 font-medium"
                        onClick={() => setIsOpen(false)}
                      >
                        Sign In
                      </Link>
                      <Link
                        href="/signup"
                        onClick={() => setIsOpen(false)}
                      >
                        <Button className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white border-0 shadow-lg shadow-emerald-600/25 transition-all duration-300 rounded-xl">
                          Get Started
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}