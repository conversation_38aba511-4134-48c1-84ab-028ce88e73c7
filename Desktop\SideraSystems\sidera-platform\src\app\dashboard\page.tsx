"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { WorkflowAuth } from "@/components/workflow-auth"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/lib/supabase/types"
import { WorkflowAuthConfig } from "@/lib/types/auth"
import { 
  Star, 
  MessageSquare, 
  Share2, 
  Clock, 
  Settings, 
  Play, 
  Pause, 
  BarChart3,
  Calendar,
  CheckCircle,
  AlertCircle,
  Zap,
  Loader2
} from "lucide-react"

type AutomationConfig = Database['public']['Tables']['automation_configs']['Row']
type DatabaseAuthConfig = Database['public']['Tables']['workflow_auth_configs']['Row']
type LogEntry = Database['public']['Tables']['logs']['Row']

// Helper function to convert database auth config to component format
const convertAuthConfig = (dbConfig: DatabaseAuthConfig): WorkflowAuthConfig => {
  return {
    id: dbConfig.id,
    user_id: dbConfig.user_id,
    workflow_type: dbConfig.workflow_type,
    provider: dbConfig.provider,
    auth_method: dbConfig.auth_method,
    status: dbConfig.status,
    created_at: dbConfig.created_at,
    updated_at: dbConfig.updated_at,
    expires_at: dbConfig.expires_at,
    provider_user_id: dbConfig.provider_user_id,
    provider_username: dbConfig.provider_username,
    ...dbConfig.auth_data
  } as WorkflowAuthConfig
}

interface DashboardStats {
  activeAutomations: number
  totalAutomations: number
  reviewsThisWeek: number
  responsesSent: number
  postsPublished: number
}

const automationDetails = {
  review_booster: {
    title: 'Review Booster',
    description: 'Automatically request reviews from satisfied customers',
    icon: <Star className="h-5 w-5" />,
    color: 'text-green-400'
  },
  seo_responder: {
    title: 'SEO Responder',
    description: 'AI-powered responses to Google Business reviews',
    icon: <MessageSquare className="h-5 w-5" />,
    color: 'text-blue-400'
  },
  social_poster: {
    title: 'Social Poster',
    description: 'Automated educational content posting',
    icon: <Share2 className="h-5 w-5" />,
    color: 'text-purple-400'
  }
}

export default function Dashboard() {
  const [selectedAutomation, setSelectedAutomation] = useState<string | null>(null)
  const [automations, setAutomations] = useState<AutomationConfig[]>([])
  const [authConfigs, setAuthConfigs] = useState<DatabaseAuthConfig[]>([])
  const [recentLogs, setRecentLogs] = useState<LogEntry[]>([])
  const [stats, setStats] = useState<DashboardStats>({
    activeAutomations: 0,
    totalAutomations: 0,
    reviewsThisWeek: 0,
    responsesSent: 0,
    postsPublished: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const supabase = createClient()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        setError('Please log in to view your dashboard')
        return
      }

      // Fetch automations, auth configs, and logs in parallel
      const [automationsRes, authConfigsRes, logsRes] = await Promise.all([
        supabase
          .from('automation_configs')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false }),
        
        supabase
          .from('workflow_auth_configs')
          .select('*')
          .eq('user_id', user.id)
          .eq('status', 'connected'),
        
        supabase
          .from('logs')
          .select('*')
          .eq('user_id', user.id)
          .order('timestamp', { ascending: false })
          .limit(10)
      ])

      if (automationsRes.error) throw automationsRes.error
      if (authConfigsRes.error) throw authConfigsRes.error
      if (logsRes.error) throw logsRes.error

      setAutomations(automationsRes.data || [])
      setAuthConfigs(authConfigsRes.data || [])
      setRecentLogs(logsRes.data || [])

      // Calculate stats
      await calculateStats(user.id, automationsRes.data || [])
      
    } catch (err) {
      console.error('Error loading dashboard:', err)
      setError('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = async (userId: string, automationConfigs: AutomationConfig[]) => {
    try {
      const activeAutomations = automationConfigs.filter(a => a.is_active).length
      const totalAutomations = automationConfigs.length

      // Calculate date ranges
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)

      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

      // Fetch analytics data in parallel
      const [reviewLogsRes, responseLogsRes, postLogsRes] = await Promise.all([
        supabase
          .from('logs')
          .select('*')
          .eq('user_id', userId)
          .eq('automation_type', 'review_booster')
          .eq('event_type', 'success')
          .gte('timestamp', oneWeekAgo.toISOString()),
        
        supabase
          .from('logs')
          .select('*')
          .eq('user_id', userId)
          .eq('automation_type', 'seo_responder')
          .eq('event_type', 'success')
          .gte('timestamp', oneWeekAgo.toISOString()),
        
        supabase
          .from('logs')
          .select('*')
          .eq('user_id', userId)
          .eq('automation_type', 'social_poster')
          .eq('event_type', 'success')
          .gte('timestamp', oneMonthAgo.toISOString())
      ])

      setStats({
        activeAutomations,
        totalAutomations,
        reviewsThisWeek: reviewLogsRes.data?.length || 0,
        responsesSent: responseLogsRes.data?.length || 0,
        postsPublished: postLogsRes.data?.length || 0
      })
    } catch (err) {
      console.error('Error calculating stats:', err)
    }
  }

  const handleToggleAutomation = async (automationId: string) => {
    try {
      const automation = automations.find(a => a.id === automationId)
      if (!automation) return

      const newStatus = !automation.is_active
      
      // Update via API endpoint
      const response = await fetch('/api/automation-config', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          automationId,
          isActive: newStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update automation')
      }

      // Update local state
      setAutomations(prev => prev.map(a => 
        a.id === automationId 
          ? { ...a, is_active: newStatus }
          : a
      ))

      // Recalculate stats
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const updatedAutomations = automations.map(a => 
          a.id === automationId ? { ...a, is_active: newStatus } : a
        )
        await calculateStats(user.id, updatedAutomations)
      }
      
    } catch (err) {
      console.error('Error toggling automation:', err)
      setError('Failed to update automation')
    }
  }

  const handleAuthConnect = async (provider: string, authMethod: string, data: any) => {
    try {
      const response = await fetch('/api/workflow-auth', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          workflowType: selectedAutomation ? automations.find(a => a.id === selectedAutomation)?.automation_type : 'review_booster',
          provider,
          authMethod,
          authData: data
        })
      })

      if (!response.ok) {
        throw new Error('Failed to connect authentication')
      }

      // Reload auth configs
      await loadDashboardData()
      
    } catch (err) {
      console.error('Error connecting auth:', err)
      setError('Failed to connect authentication')
    }
  }

  const handleAuthDisconnect = async (configId: string) => {
    try {
      await supabase
        .from('workflow_auth_configs')
        .delete()
        .eq('id', configId)

      // Reload auth configs
      await loadDashboardData()
      
    } catch (err) {
      console.error('Error disconnecting auth:', err)
      setError('Failed to disconnect authentication')
    }
  }

  const formatCronSchedule = (cron: string) => {
    const scheduleMap: Record<string, string> = {
      '0 9 * * *': 'Daily at 9:00 AM',
      '0 */4 * * *': 'Every 4 hours',
      '0 10 * * 1': 'Weekly on Monday at 10:00 AM',
      '0 0 * * 0': 'Weekly on Sunday at midnight'
    }
    return scheduleMap[cron] || cron
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto py-8 px-4 max-w-7xl">
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="text-lg">Loading your dashboard...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto py-8 px-4 max-w-7xl">
          <div className="flex items-center justify-center h-64">
            <Card className="border-0 bg-card/50 backdrop-blur max-w-md">
              <CardContent className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
                <p className="text-muted-foreground mb-4">{error}</p>
                <Button onClick={loadDashboardData} variant="outline">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your marketing automations and view performance analytics.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Active Automations</CardTitle>
                <Zap className="h-4 w-4 text-green-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeAutomations}</div>
              <p className="text-xs text-muted-foreground">out of {stats.totalAutomations} workflows</p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Reviews This Week</CardTitle>
                <Star className="h-4 w-4 text-yellow-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.reviewsThisWeek}</div>
              <p className="text-xs text-muted-foreground">automated requests</p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Responses Sent</CardTitle>
                <MessageSquare className="h-4 w-4 text-blue-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.responsesSent}</div>
              <p className="text-xs text-muted-foreground">this week</p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Posts Published</CardTitle>
                <Share2 className="h-4 w-4 text-purple-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.postsPublished}</div>
              <p className="text-xs text-muted-foreground">this month</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Automations List */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Your Automations</h2>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Manage All
              </Button>
            </div>

            <div className="space-y-4">
              {automations.length > 0 ? (
                automations.map((automation) => {
                  const details = automationDetails[automation.automation_type]
                  const isSelected = selectedAutomation === automation.id

                  return (
                    <Card 
                      key={automation.id} 
                      className={`border-0 bg-card/50 backdrop-blur transition-all duration-200 cursor-pointer hover:shadow-lg ${
                        isSelected ? 'ring-2 ring-brand-600' : ''
                      }`}
                      onClick={() => setSelectedAutomation(isSelected ? null : automation.id)}
                    >
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg bg-secondary/50 ${details.color}`}>
                            {details.icon}
                          </div>
                          <div>
                            <CardTitle className="text-base">{details.title}</CardTitle>
                            <CardDescription className="text-sm">
                              {details.description}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={automation.is_active ? 'success' : 'secondary'}>
                            {automation.is_active ? 'Active' : 'Paused'}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleToggleAutomation(automation.id)
                            }}
                          >
                            {automation.is_active ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Schedule:</span>
                          <span>{formatCronSchedule(automation.cron_schedule)}</span>
                        </div>
                        
                        {automation.last_run && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Last run:</span>
                            <span>{new Date(automation.last_run).toLocaleDateString()}</span>
                          </div>
                        )}
                        
                        {automation.next_run && automation.is_active && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Next run:</span>
                            <span>{new Date(automation.next_run).toLocaleDateString()}</span>
                          </div>
                        )}

                        {automation.workflow_id ? (
                          <div className="flex items-center gap-2 text-sm text-green-400">
                            <CheckCircle className="h-3 w-3" />
                            Workflow configured
                          </div>
                        ) : (
                          <div className="flex items-center gap-2 text-sm text-yellow-400">
                            <AlertCircle className="h-3 w-3" />
                            Setup required
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                  )
                })
              ) : (
                <Card className="border-0 bg-card/50 backdrop-blur">
                  <CardContent className="text-center py-12">
                    <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Automations Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      Create your first automation to start growing your business.
                    </p>
                    <Button variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      Create Automation
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Authentication Setup */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Account Connections</h2>
              <Button variant="outline" size="sm">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
            </div>

            {selectedAutomation ? (
              <WorkflowAuth
                workflowType={automations.find(a => a.id === selectedAutomation)?.automation_type || 'review_booster'}
                authConfigs={authConfigs
                  .filter(config => 
                    config.workflow_type === automations.find(a => a.id === selectedAutomation)?.automation_type
                  )
                  .map(convertAuthConfig)
                }
                onAuthConnect={handleAuthConnect}
                onAuthDisconnect={handleAuthDisconnect}
              />
            ) : (
              <Card className="border-0 bg-card/50 backdrop-blur">
                <CardContent className="text-center py-12">
                  <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Select an Automation</h3>
                  <p className="text-muted-foreground mb-4">
                    Choose an automation from the left to configure its account connections and settings.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardContent className="p-6">
              {recentLogs.length > 0 ? (
                <div className="space-y-4">
                  {recentLogs.map((log) => {
                    const details = automationDetails[log.automation_type]
                    const timeAgo = new Date(log.timestamp).toLocaleString()
                    
                    return (
                      <div key={log.id} className="flex items-center gap-4 p-3 rounded-lg bg-secondary/20">
                        <div className={`p-2 rounded-full bg-${log.event_type === 'success' ? 'green' : log.event_type === 'error' ? 'red' : 'yellow'}-600/10`}>
                          {details.icon}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">{log.message}</p>
                          <p className="text-xs text-muted-foreground">
                            {details.title} • {timeAgo}
                          </p>
                        </div>
                        <Badge 
                          variant={log.event_type === 'success' ? 'success' : 
                                  log.event_type === 'error' ? 'destructive' : 'secondary'}
                        >
                          {log.event_type}
                        </Badge>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Recent Activity</h3>
                  <p className="text-muted-foreground">
                    Automation activity will appear here once your workflows start running.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}