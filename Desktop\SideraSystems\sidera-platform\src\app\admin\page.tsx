"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Users, 
  Settings, 
  Activity, 
  DollarSign,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Pause,
  Play,
  Trash2,
  Eye,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react"

// Mock data - in real app this would come from Supabase
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    full_name: '<PERSON>',
    business_name: 'ACME HVAC',
    business_category: 'HVAC',
    subscription_tier: 'standard',
    subscription_status: 'active',
    created_at: '2024-01-15T10:00:00Z',
    automations: [
      { type: 'review_booster', active: true, last_run: '2024-01-30T09:00:00Z' },
      { type: 'seo_responder', active: false, last_run: null }
    ]
  },
  {
    id: '2',
    email: '<EMAIL>',
    full_name: '<PERSON>',
    business_name: 'Fix-It Plumbing',
    business_category: 'Plumbing',
    subscription_tier: 'premium',
    subscription_status: 'active',
    created_at: '2024-01-10T14:30:00Z',
    automations: [
      { type: 'review_booster', active: true, last_run: '2024-01-30T09:00:00Z' },
      { type: 'seo_responder', active: true, last_run: '2024-01-30T11:00:00Z' },
      { type: 'social_poster', active: true, last_run: '2024-01-29T10:00:00Z' }
    ]
  },
  {
    id: '3',
    email: '<EMAIL>',
    full_name: 'Mike Wilson',
    business_name: 'Spark Electric',
    business_category: 'Electrical',
    subscription_tier: 'basic',
    subscription_status: 'past_due',
    created_at: '2024-01-20T16:45:00Z',
    automations: [
      { type: 'review_booster', active: false, last_run: '2024-01-25T09:00:00Z' }
    ]
  }
]

const mockSystemStats = {
  totalUsers: 847,
  activeAutomations: 2341,
  monthlyRevenue: 84720,
  uptime: 99.9
}

export default function AdminDashboard() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<string | null>(null)
  const [filterTier, setFilterTier] = useState('all')

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.business_name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesTier = filterTier === 'all' || user.subscription_tier === filterTier
    
    return matchesSearch && matchesTier
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'past_due': return 'destructive'
      case 'canceled': return 'secondary'
      default: return 'secondary'
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'basic': return 'secondary'
      case 'standard': return 'default'
      case 'premium': return 'success'
      default: return 'secondary'
    }
  }

  const handleUserAction = (action: string, userId: string) => {
    console.log(`Admin action: ${action} for user ${userId}`)
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8 px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Manage users, monitor system performance, and oversee all automations.
          </p>
        </div>

        {/* System Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-blue-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockSystemStats.totalUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+12% from last month</p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Active Automations</CardTitle>
                <Activity className="h-4 w-4 text-green-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockSystemStats.activeAutomations.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">Across all users</p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-yellow-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${mockSystemStats.monthlyRevenue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">+8% from last month</p>
            </CardContent>
          </Card>

          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
                <TrendingUp className="h-4 w-4 text-purple-400" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockSystemStats.uptime}%</div>
              <p className="text-xs text-muted-foreground">Last 30 days</p>
            </CardContent>
          </Card>
        </div>

        {/* Users Management */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">User Management</h2>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <select
                value={filterTier}
                onChange={(e) => setFilterTier(e.target.value)}
                className="flex h-10 w-32 rounded-md border border-input bg-input px-3 py-2 text-sm"
              >
                <option value="all">All Plans</option>
                <option value="basic">Basic</option>
                <option value="standard">Standard</option>
                <option value="premium">Premium</option>
              </select>
            </div>
          </div>

          {/* Users Table */}
          <Card className="border-0 bg-card/50 backdrop-blur">
            <CardHeader>
              <CardTitle>All Users ({filteredUsers.length})</CardTitle>
              <CardDescription>
                Manage user accounts, subscriptions, and automation configurations.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredUsers.map((user) => (
                  <div 
                    key={user.id}
                    className="flex items-center justify-between p-4 rounded-lg border bg-secondary/20 hover:bg-secondary/30 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium">{user.full_name}</h3>
                            <Badge variant={getStatusColor(user.subscription_status) as any}>
                              {user.subscription_status}
                            </Badge>
                            <Badge variant={getTierColor(user.subscription_tier) as any}>
                              {user.subscription_tier}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                          <p className="text-sm text-muted-foreground">{user.business_name} • {user.business_category}</p>
                        </div>
                        
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {user.automations.filter(a => a.active).length} active automations
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Joined {new Date(user.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      {/* Automation Status */}
                      <div className="mt-3 flex items-center gap-2">
                        {user.automations.map((automation, index) => (
                          <div key={index} className="flex items-center gap-1 text-xs">
                            {automation.active ? (
                              <CheckCircle className="h-3 w-3 text-green-400" />
                            ) : (
                              <Clock className="h-3 w-3 text-muted-foreground" />
                            )}
                            <span className="capitalize">{automation.type.replace('_', ' ')}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUserAction('view', user.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUserAction('edit', user.id)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUserAction('suspend', user.id)}
                      >
                        <Pause className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUserAction('more', user.id)}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {filteredUsers.length === 0 && (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No users found</h3>
                  <p className="text-muted-foreground">
                    {searchTerm || filterTier !== 'all' 
                      ? 'Try adjusting your search or filters.'
                      : 'No users have signed up yet.'
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* System Health */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">System Health</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-0 bg-card/50 backdrop-blur">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Workflow Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Running Workflows</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-sm font-medium">1,847</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Failed Workflows</span>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-destructive" />
                      <span className="text-sm font-medium">12</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Paused Workflows</span>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-400" />
                      <span className="text-sm font-medium">94</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 bg-card/50 backdrop-blur">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <Button variant="outline" size="sm">
                    View Logs
                  </Button>
                  <Button variant="outline" size="sm">
                    System Settings
                  </Button>
                  <Button variant="outline" size="sm">
                    Export Data
                  </Button>
                  <Button variant="outline" size="sm">
                    Send Notification
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}