{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow-lg hover:shadow-xl transition-all duration-200\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wGACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2VACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\r\nimport { Database } from './types'\r\n\r\nexport function createClient() {\r\n  return createBrowserClient<Database>(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  )\r\n}"], "names": [], "mappings": ";;;AAKI;AALJ;AAAA;;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/app/login/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { use<PERSON><PERSON><PERSON> } from \"next/navigation\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Input } from \"@/components/ui/input\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { \n  Loader2, \n  Mail, \n  Lock,\n  ArrowRight,\n  AlertCircle\n} from \"lucide-react\"\n\nexport default function Login() {\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  })\n  const router = useRouter()\n  const supabase = createClient()\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    if (error) setError(null) // Clear error when user starts typing\n  }\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const { data, error: authError } = await supabase.auth.signInWithPassword({\n        email: formData.email,\n        password: formData.password,\n      })\n\n      if (authError) {\n        setError(authError.message)\n        return\n      }\n\n      if (data.user) {\n        // Successful login - redirect to dashboard\n        router.push('/dashboard')\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      setError('An unexpected error occurred. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-slate-950 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden\">\n      {/* Background effects */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-950 to-slate-900\" />\n      <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(120,119,198,0.1),transparent_50%)]\" />\n      <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_70%_60%,rgba(16,185,129,0.1),transparent_50%)]\" />\n      \n      <div className=\"max-w-md w-full space-y-10 relative z-10\">\n        <div className=\"text-center\">\n          <Link href=\"/\" className=\"flex items-center justify-center space-x-3 mb-10\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent\">\n                Proof Engine\n              </div>\n              <div className=\"text-sm text-slate-400 font-medium\">\n                by Sidera Systems\n              </div>\n            </div>\n          </Link>\n          \n          <h2 className=\"text-4xl font-bold bg-gradient-to-r from-white via-slate-100 to-slate-300 bg-clip-text text-transparent mb-3\">\n            Welcome back\n          </h2>\n          <p className=\"text-lg text-slate-400 leading-relaxed\">\n            Sign in to your account to continue\n          </p>\n        </div>\n\n        <Card className=\"border-0 bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-xl border border-slate-700/50 shadow-2xl\">\n          <CardHeader className=\"pb-6\">\n            <CardTitle className=\"text-2xl font-semibold text-slate-100\">Sign In</CardTitle>\n            <CardDescription className=\"text-slate-400 leading-relaxed\">\n              Enter your credentials to access your dashboard\n            </CardDescription>\n          </CardHeader>\n          \n          <CardContent>\n            <form onSubmit={handleLogin} className=\"space-y-8\">\n              {error && (\n                <div className=\"flex items-center gap-3 p-4 bg-red-950/50 border border-red-900/50 rounded-lg\">\n                  <AlertCircle className=\"h-5 w-5 text-red-400 flex-shrink-0\" />\n                  <p className=\"text-red-300 text-sm\">{error}</p>\n                </div>\n              )}\n              \n              <div className=\"space-y-5\">\n                <div className=\"relative\">\n                  <Mail className=\"absolute left-4 top-4 h-5 w-5 text-slate-500\" />\n                  <Input\n                    type=\"email\"\n                    placeholder=\"Email Address\"\n                    value={formData.email}\n                    onChange={(e) => handleInputChange('email', e.target.value)}\n                    className=\"pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"relative\">\n                  <Lock className=\"absolute left-4 top-4 h-5 w-5 text-slate-500\" />\n                  <Input\n                    type=\"password\"\n                    placeholder=\"Password\"\n                    value={formData.password}\n                    onChange={(e) => handleInputChange('password', e.target.value)}\n                    className=\"pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"remember-me\"\n                    name=\"remember-me\"\n                    type=\"checkbox\"\n                    className=\"h-4 w-4 rounded border-slate-600 bg-slate-800/50 text-emerald-500 focus:ring-emerald-500/20 focus:ring-offset-0\"\n                  />\n                  <label htmlFor=\"remember-me\" className=\"ml-3 block text-sm text-slate-400\">\n                    Remember me\n                  </label>\n                </div>\n\n                <div className=\"text-sm\">\n                  <Link\n                    href=\"/forgot-password\"\n                    className=\"font-medium text-emerald-400 hover:text-emerald-300 transition-colors\"\n                  >\n                    Forgot password?\n                  </Link>\n                </div>\n              </div>\n\n              <Button \n                type=\"submit\"\n                className=\"w-full h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-lg shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-[1.02] hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n                disabled={loading || !formData.email || !formData.password}\n              >\n                {loading ? (\n                  <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                ) : (\n                  <>\n                    Sign In\n                    <ArrowRight className=\"ml-2 h-5 w-5\" />\n                  </>\n                )}\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n\n        <div className=\"text-center\">\n          <p className=\"text-slate-400\">\n            Don't have an account?{' '}\n            <Link href=\"/signup\" className=\"font-medium text-emerald-400 hover:text-emerald-300 transition-colors\">\n              Sign up for free\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,OAAO,SAAS,OAAM,sCAAsC;IAClE;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACxE,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;YAC7B;YAEA,IAAI,WAAW;gBACb,SAAS,UAAU,OAAO;gBAC1B;YACF;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,2CAA2C;gBAC3C,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA4F;;;;;;sDAG3G,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;;;;;;;;;;;;0CAMxD,6LAAC;gCAAG,WAAU;0CAA+G;;;;;;0CAG7H,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;kCAKxD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAwC;;;;;;kDAC7D,6LAAC,mIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAiC;;;;;;;;;;;;0CAK9D,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAK,UAAU;oCAAa,WAAU;;wCACpC,uBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACZ,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4DAC1D,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACZ,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC7D,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,WAAU;;;;;;sEAEZ,6LAAC;4DAAM,SAAQ;4DAAc,WAAU;sEAAoC;;;;;;;;;;;;8DAK7E,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;sDAML,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU,WAAW,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ;sDAEzD,wBACC,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB;;oDAAE;kEAEA,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAiB;gCACL;8CACvB,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnH;GAtKwB;;QAOP,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}]}