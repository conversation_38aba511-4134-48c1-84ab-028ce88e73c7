// Authentication types for different workflow providers
export type AuthMethod = 'oauth' | 'api_key' | 'webhook' | 'email_password'

export interface BaseAuthConfig {
  id: string
  user_id: string
  workflow_type: 'review_booster' | 'seo_responder' | 'social_poster'
  provider: string
  auth_method: AuthMethod
  status: 'connected' | 'expired' | 'error' | 'pending'
  created_at: string
  updated_at: string
}

// OAuth configuration (e.g., Facebook, Google)
export interface OAuthConfig extends BaseAuthConfig {
  auth_method: 'oauth'
  access_token: string
  refresh_token?: string
  expires_at?: string
  scope: string[]
  provider_user_id: string
  provider_username?: string
}

// API Key configuration (e.g., SendGrid, Twilio)
export interface ApiKeyConfig extends BaseAuthConfig {
  auth_method: 'api_key'
  api_key: string
  api_secret?: string
  endpoint?: string
  additional_headers?: Record<string, string>
}

// Webhook configuration (reverse API)
export interface WebhookConfig extends BaseAuthConfig {
  auth_method: 'webhook'
  webhook_url: string
  webhook_secret: string
  events: string[]
}

// Email/Password configuration
export interface EmailPasswordConfig extends BaseAuthConfig {
  auth_method: 'email_password'
  email: string
  password: string // encrypted
  smtp_server?: string
  smtp_port?: number
}

export type WorkflowAuthConfig = OAuthConfig | ApiKeyConfig | WebhookConfig | EmailPasswordConfig

// Workflow provider definitions
export interface WorkflowProvider {
  id: string
  name: string
  description: string
  workflow_types: string[]
  auth_method: AuthMethod
  required_scopes?: string[]
  auth_url?: string
  token_url?: string
  client_id?: string
  setup_instructions: string
  icon: string
}

// ONLY TWO WORKFLOWS SUPPORTED
export const WORKFLOW_PROVIDERS: Record<string, WorkflowProvider[]> = {
  // 1. SEO Responder - Google review management via AI replies with SEO
  seo_responder: [
    {
      id: 'google_business',
      name: 'Google Business Profile',
      description: 'Connect to manage and respond to Google Business reviews with AI',
      workflow_types: ['seo_responder'],
      auth_method: 'api_key', // Client enters their Google Business API key
      setup_instructions: 'Enter your Google Business API key from Google Cloud Console. We will use AI to generate SEO-optimized responses to your reviews.',
      icon: 'star'
    }
  ],
  
  // 2. Social Poster - Facebook business page posting
  social_poster: [
    {
      id: 'facebook',
      name: 'Facebook Business Pages',
      description: 'Connect your Facebook business page for automated posting',
      workflow_types: ['social_poster'],
      auth_method: 'oauth', // OAuth to OUR Facebook app
      required_scopes: ['pages_manage_posts', 'pages_read_engagement', 'pages_show_list'],
      auth_url: 'https://www.facebook.com/v18.0/dialog/oauth',
      setup_instructions: 'Connect your Facebook business page to automatically post educational content and updates.',
      icon: 'facebook'
    }
  ]
}

export interface WorkflowAuthState {
  [workflowType: string]: {
    [providerId: string]: WorkflowAuthConfig | null
  }
}