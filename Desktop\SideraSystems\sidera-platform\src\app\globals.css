@import "tailwindcss";

@theme inline {
  /* Dark Matte Theme Colors */
  --color-background: #0a0a0a;
  --color-foreground: #ededed;
  --color-card: #161616;
  --color-card-foreground: #ededed;
  --color-popover: #161616;
  --color-popover-foreground: #ededed;
  --color-primary: #ededed;
  --color-primary-foreground: #0a0a0a;
  --color-secondary: #1c1c1c;
  --color-secondary-foreground: #ededed;
  --color-muted: #262626;
  --color-muted-foreground: #a3a3a3;
  --color-accent: #2d2d2d;
  --color-accent-foreground: #ededed;
  --color-destructive: #dc2626;
  --color-destructive-foreground: #fafafa;
  --color-border: #2d2d2d;
  --color-input: #1c1c1c;
  --color-ring: #525252;
  
  /* Brand Colors */
  --color-brand-50: #f8fafc;
  --color-brand-100: #f1f5f9;
  --color-brand-200: #e2e8f0;
  --color-brand-300: #cbd5e1;
  --color-brand-400: #94a3b8;
  --color-brand-500: #64748b;
  --color-brand-600: #475569;
  --color-brand-700: #334155;
  --color-brand-800: #1e293b;
  --color-brand-900: #0f172a;
  
  /* Gradients */
  --gradient-brand: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --gradient-accent: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  
  /* Shadows */
  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-card-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  
  /* Fonts */
  --font-sans: ui-sans-serif, system-ui, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, monospace;
  
  /* Radius */
  --radius: 0.75rem;
}

body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background);
}

::-webkit-scrollbar-thumb {
  background: var(--color-muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
}

/* Selection */
::selection {
  background: var(--color-accent);
  color: var(--color-accent-foreground);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.6s ease-out forwards;
}