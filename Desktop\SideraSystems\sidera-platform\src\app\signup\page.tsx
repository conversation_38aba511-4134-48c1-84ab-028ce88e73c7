"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { createClient } from "@/lib/supabase/client"
import { 
  AlertCircle,
  Loader2, 
  Mail, 
  Lock, 
  User, 
  Building, 
  MapPin,
  CheckCircle,
  ArrowRight
} from "lucide-react"

const businessCategories = [
  'HVAC',
  'Plumbing', 
  'Electrical',
  'Roofing',
  'Landscaping',
  'Cleaning Services',
  'Handyman',
  'Pest Control',
  'Appliance Repair',
  'Other'
]

export default function SignUp() {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    fullName: '',
    businessName: '',
    businessCategory: '',
    serviceArea: '',
    plan: 'standard'
  })
  const router = useRouter()
  const supabase = createClient()

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (error) setError(null) // Clear error when user starts typing
  }

  const handleNextStep = () => {
    if (step < 3) {
      setStep(step + 1)
    }
  }

  const handleSignUp = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Create auth user
      const { data, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            full_name: formData.fullName,
            business_name: formData.businessName,
            business_category: formData.businessCategory,
            service_area: formData.serviceArea,
          }
        }
      })

      if (authError) {
        setError(authError.message)
        return
      }

      if (data.user) {
        // Create user profile in database
        const { error: profileError } = await supabase
          .from('client_configs')
          .insert({
            user_id: data.user.id,
            business_name: formData.businessName,
            business_category: formData.businessCategory,
            service_area: formData.serviceArea,
            subscription_plan: formData.plan,
          })

        if (profileError) {
          console.error('Profile creation error:', profileError)
          // Continue anyway - user is created
        }

        // Redirect to onboarding
        router.push('/onboarding')
      }
    } catch (error) {
      console.error('Sign up error:', error)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const plans = [
    {
      name: 'Basic',
      price: 49,
      description: 'Perfect for getting started',
      features: [
        '1 Automation',
        '500 Review Requests/month',
        'Basic Analytics',
        'Email Support'
      ]
    },
    {
      name: 'Standard',
      price: 99,
      description: 'Most popular choice',
      features: [
        '3 Automations',
        '2,000 Review Requests/month',
        'Advanced Analytics',
        'Priority Support',
        'SEO Auto-Replies'
      ],
      popular: true
    },
    {
      name: 'Premium',
      price: 199,
      description: 'For established businesses',
      features: [
        'Unlimited Automations',
        'Unlimited Review Requests',
        'Full Analytics Suite',
        '24/7 Support',
        'Custom Workflows'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-slate-950 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-950 to-slate-900" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(120,119,198,0.1),transparent_50%)]" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_60%,rgba(16,185,129,0.1),transparent_50%)]" />
      
      <div className="max-w-md w-full space-y-10 relative z-10">
        <div className="text-center">
          <Link href="/" className="flex items-center justify-center space-x-3 mb-10">
            <div className="text-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                Proof Engine
              </div>
              <div className="text-sm text-slate-400 font-medium">
                by Sidera Systems
              </div>
            </div>
          </Link>
          
          <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-slate-100 to-slate-300 bg-clip-text text-transparent mb-3">
            Create your account
          </h2>
          <p className="text-lg text-slate-400 leading-relaxed">
            Start building your AI marketing engine today
          </p>
        </div>

        <Card className="border-0 bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 backdrop-blur-xl border border-slate-700/50 shadow-2xl">
          <CardHeader className="pb-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-semibold text-slate-100">
                Step {step} of 3
              </CardTitle>
              <div className="flex space-x-3">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      i <= step 
                        ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 shadow-lg shadow-emerald-500/25' 
                        : 'bg-slate-700'
                    }`}
                  />
                ))}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {step === 1 && (
              <div className="space-y-6">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-semibold text-slate-100 mb-2">Account Details</h3>
                  <p className="text-slate-400 leading-relaxed">
                    Let's start with your basic information
                  </p>
                </div>

                {error && (
                  <div className="flex items-center gap-3 p-4 bg-red-950/50 border border-red-900/50 rounded-lg">
                    <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
                    <p className="text-red-300 text-sm">{error}</p>
                  </div>
                )}

                <div className="space-y-5">
                  <div className="relative">
                    <User className="absolute left-4 top-4 h-5 w-5 text-slate-500" />
                    <Input
                      placeholder="Full Name"
                      value={formData.fullName}
                      onChange={(e) => handleInputChange('fullName', e.target.value)}
                      className="pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                    />
                  </div>
                  
                  <div className="relative">
                    <Mail className="absolute left-4 top-4 h-5 w-5 text-slate-500" />
                    <Input
                      type="email"
                      placeholder="Email Address"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                    />
                  </div>
                  
                  <div className="relative">
                    <Lock className="absolute left-4 top-4 h-5 w-5 text-slate-500" />
                    <Input
                      type="password"
                      placeholder="Password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      className="pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                    />
                  </div>
                </div>

                <Button 
                  onClick={handleNextStep}
                  className="w-full h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-lg shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-[1.02] hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  disabled={!formData.fullName || !formData.email || !formData.password}
                >
                  Continue
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-6">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-semibold text-slate-100 mb-2">Business Information</h3>
                  <p className="text-slate-400 leading-relaxed">
                    Tell us about your business
                  </p>
                </div>

                <div className="space-y-5">
                  <div className="relative">
                    <Building className="absolute left-4 top-4 h-5 w-5 text-slate-500" />
                    <Input
                      placeholder="Business Name"
                      value={formData.businessName}
                      onChange={(e) => handleInputChange('businessName', e.target.value)}
                      className="pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                    />
                  </div>
                  
                  <div className="relative">
                    <select
                      value={formData.businessCategory}
                      onChange={(e) => handleInputChange('businessCategory', e.target.value)}
                      className="h-12 w-full rounded-lg border border-slate-700/50 bg-slate-800/50 px-4 py-3 text-slate-100 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/20 focus-visible:border-emerald-500 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="" className="bg-slate-800 text-slate-400">Select Business Category</option>
                      {businessCategories.map(category => (
                        <option key={category} value={category} className="bg-slate-800 text-slate-100">{category}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="relative">
                    <MapPin className="absolute left-4 top-4 h-5 w-5 text-slate-500" />
                    <Input
                      placeholder="Service Area (e.g., Chicago, IL)"
                      value={formData.serviceArea}
                      onChange={(e) => handleInputChange('serviceArea', e.target.value)}
                      className="pl-12 h-12 bg-slate-800/50 border-slate-700/50 text-slate-100 placeholder-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                    />
                  </div>
                </div>

                <Button 
                  onClick={handleNextStep}
                  className="w-full h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-lg shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-[1.02] hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  disabled={!formData.businessName || !formData.businessCategory || !formData.serviceArea}
                >
                  Continue
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-6">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-semibold text-slate-100 mb-2">Choose Your Plan</h3>
                  <p className="text-slate-400 leading-relaxed">
                    Start with a 14-day free trial
                  </p>
                </div>

                <div className="grid gap-5">
                  {plans.map((plan) => (
                    <div
                      key={plan.name}
                      className={`relative border rounded-xl p-6 cursor-pointer transition-all duration-300 ${
                        formData.plan === plan.name.toLowerCase()
                          ? 'border-emerald-500 bg-gradient-to-br from-emerald-900/20 to-emerald-800/10 shadow-lg shadow-emerald-500/25'
                          : 'border-slate-700/50 bg-slate-800/30 hover:border-slate-600 hover:bg-slate-800/50'
                      }`}
                      onClick={() => handleInputChange('plan', plan.name.toLowerCase())}
                    >
                      {plan.popular && (
                        <Badge className="absolute -top-3 left-6 bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-lg">
                          Most Popular
                        </Badge>
                      )}
                      
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-slate-100 text-lg">{plan.name}</h4>
                        <div className="text-right">
                          <span className="text-3xl font-bold text-slate-100">${plan.price}</span>
                          <span className="text-sm text-slate-400">/mo</span>
                        </div>
                      </div>
                      
                      <p className="text-slate-400 mb-4 leading-relaxed">{plan.description}</p>
                      
                      <ul className="space-y-2">
                        {plan.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-sm text-slate-300">
                            <CheckCircle className="h-4 w-4 text-emerald-400 mr-3 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>

                <Button 
                  onClick={handleSignUp}
                  className="w-full h-12 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-lg shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-[1.02] hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  ) : (
                    'Start Free Trial'
                  )}
                </Button>

                <p className="text-sm text-center text-slate-400">
                  No credit card required • Cancel anytime
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-slate-400">
            Already have an account?{' '}
            <Link href="/login" className="font-medium text-emerald-400 hover:text-emerald-300 transition-colors">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}