import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!)

// POST /api/generate-blog - Generate blog post using Gemini AI
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin (only admins can trigger blog generation)
    const { data: profile } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.email === process.env.ADMIN_EMAIL
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { 
      topic, 
      businessCategory = 'Home Services',
      targetLocation = 'United States',
      keywords = [],
      customPrompt 
    } = body

    // Generate blog post using Gemini
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-pro' })

    const prompt = customPrompt || generateBlogPrompt(topic, businessCategory, targetLocation, keywords)

    const result = await model.generateContent(prompt)
    const response = await result.response
    const generatedContent = response.text()

    // Parse the generated content to extract title, content, etc.
    const parsedContent = parseBlogContent(generatedContent)

    // Generate SEO-friendly slug
    const slug = generateSlug(parsedContent.title)

    // Check if slug already exists
    const { data: existingPost } = await supabase
      .from('posts')
      .select('slug')
      .eq('slug', slug)
      .single()

    const finalSlug = existingPost ? `${slug}-${Date.now()}` : slug

    // Save blog post to database
    const { data: blogPost, error: insertError } = await supabase
      .from('posts')
      .insert({
        title: parsedContent.title,
        slug: finalSlug,
        content: parsedContent.content,
        excerpt: parsedContent.excerpt,
        category: businessCategory,
        tags: parsedContent.tags,
        seo_keywords: keywords.length > 0 ? keywords : parsedContent.seoKeywords,
        published: false, // Admin needs to review and publish
        author: 'Sidera AI',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (insertError) {
      console.error('Database error saving blog post:', insertError)
      return NextResponse.json(
        { error: 'Failed to save blog post' },
        { status: 500 }
      )
    }

    // Log the blog generation
    await supabase
      .from('logs')
      .insert({
        user_id: user.id,
        automation_type: 'social_poster', // Blog generation relates to content creation
        event_type: 'success',
        message: `AI blog post generated: "${parsedContent.title}"`,
        details: {
          post_id: blogPost.id,
          topic,
          business_category: businessCategory,
          word_count: parsedContent.content.split(' ').length,
          keywords
        }
      })

    return NextResponse.json({
      success: true,
      blogPost,
      stats: {
        wordCount: parsedContent.content.split(' ').length,
        readingTime: Math.ceil(parsedContent.content.split(' ').length / 200), // Average reading speed
        seoScore: calculateSEOScore(parsedContent, keywords)
      }
    })

  } catch (error) {
    console.error('Blog generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate blog post' },
      { status: 500 }
    )
  }
}

// GET /api/generate-blog - Get blog generation status and recent posts
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const published = searchParams.get('published')

    // Get recent blog posts
    let query = supabase
      .from('posts')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (published !== null) {
      query = query.eq('published', published === 'true')
    }

    const { data: posts, error: postsError } = await query

    if (postsError) {
      console.error('Database error fetching posts:', postsError)
      return NextResponse.json(
        { error: 'Failed to fetch posts' },
        { status: 500 }
      )
    }

    // Get generation statistics
    const { data: stats } = await supabase
      .from('posts')
      .select('published, created_at')

    const today = new Date()
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    const thisWeek = new Date(today.setDate(today.getDate() - today.getDay()))

    const statistics = {
      total: stats?.length || 0,
      published: stats?.filter(p => p.published).length || 0,
      thisMonth: stats?.filter(p => new Date(p.created_at) >= thisMonth).length || 0,
      thisWeek: stats?.filter(p => new Date(p.created_at) >= thisWeek).length || 0
    }

    return NextResponse.json({
      posts,
      statistics
    })

  } catch (error) {
    console.error('Blog API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to generate blog prompt
function generateBlogPrompt(
  topic: string, 
  businessCategory: string, 
  targetLocation: string, 
  keywords: string[]
): string {
  const keywordText = keywords.length > 0 ? `\nTarget keywords: ${keywords.join(', ')}` : ''
  
  return `Write a comprehensive, SEO-optimized blog post for ${businessCategory} businesses in ${targetLocation}.

Topic: ${topic}${keywordText}

Please structure your response EXACTLY as follows:

TITLE: [SEO-optimized title that includes main keyword]

EXCERPT: [2-3 sentence summary for meta description, under 160 characters]

CONTENT:
[Write a 800-1000 word informative blog post that:
- Provides genuine value to homeowners
- Includes practical tips and actionable advice
- Uses a professional but approachable tone
- Naturally incorporates target keywords
- Includes subheadings for better readability
- Ends with a call-to-action encouraging readers to contact local professionals]

TAGS: [5-7 relevant tags, comma-separated]

SEO_KEYWORDS: [5-8 primary and secondary keywords, comma-separated]

Make sure the content is original, helpful, and specifically tailored to ${businessCategory} professionals and their potential customers.`
}

// Helper function to parse blog content from AI response
function parseBlogContent(content: string) {
  const sections = content.split('\n\n')
  
  let title = ''
  let excerpt = ''
  let blogContent = ''
  let tags: string[] = []
  let seoKeywords: string[] = []
  
  let currentSection = ''
  
  for (const section of sections) {
    const trimmed = section.trim()
    
    if (trimmed.startsWith('TITLE:')) {
      title = trimmed.replace('TITLE:', '').trim()
    } else if (trimmed.startsWith('EXCERPT:')) {
      excerpt = trimmed.replace('EXCERPT:', '').trim()
    } else if (trimmed.startsWith('CONTENT:')) {
      currentSection = 'content'
      blogContent = trimmed.replace('CONTENT:', '').trim()
    } else if (trimmed.startsWith('TAGS:')) {
      const tagString = trimmed.replace('TAGS:', '').trim()
      tags = tagString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    } else if (trimmed.startsWith('SEO_KEYWORDS:')) {
      const keywordString = trimmed.replace('SEO_KEYWORDS:', '').trim()
      seoKeywords = keywordString.split(',').map(kw => kw.trim()).filter(kw => kw.length > 0)
    } else if (currentSection === 'content' && trimmed.length > 0) {
      blogContent += '\n\n' + trimmed
    }
  }
  
  // Fallback if parsing fails
  if (!title) title = 'Generated Blog Post'
  if (!excerpt) excerpt = content.substring(0, 150) + '...'
  if (!blogContent) blogContent = content
  
  return {
    title,
    excerpt,
    content: blogContent,
    tags,
    seoKeywords
  }
}

// Helper function to generate URL-friendly slug
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .substring(0, 60) // Limit length
}

// Helper function to calculate basic SEO score
function calculateSEOScore(content: any, targetKeywords: string[]): number {
  let score = 0
  const maxScore = 100
  
  // Title optimization (20 points)
  if (content.title.length >= 30 && content.title.length <= 60) score += 20
  else if (content.title.length >= 20) score += 10
  
  // Excerpt optimization (15 points)
  if (content.excerpt.length >= 120 && content.excerpt.length <= 160) score += 15
  else if (content.excerpt.length >= 80) score += 10
  
  // Content length (20 points)
  const wordCount = content.content.split(' ').length
  if (wordCount >= 800 && wordCount <= 1200) score += 20
  else if (wordCount >= 500) score += 15
  else if (wordCount >= 300) score += 10
  
  // Keyword optimization (25 points)
  if (targetKeywords.length > 0) {
    const contentLower = content.content.toLowerCase()
    const titleLower = content.title.toLowerCase()
    
    let keywordScore = 0
    for (const keyword of targetKeywords) {
      const keywordLower = keyword.toLowerCase()
      if (titleLower.includes(keywordLower)) keywordScore += 5
      if (contentLower.includes(keywordLower)) keywordScore += 3
    }
    score += Math.min(keywordScore, 25)
  }
  
  // Tags optimization (10 points)
  if (content.tags.length >= 5 && content.tags.length <= 8) score += 10
  else if (content.tags.length >= 3) score += 5
  
  // Structure optimization (10 points)
  const hasSubheadings = content.content.includes('#') || content.content.includes('##')
  if (hasSubheadings) score += 10
  
  return Math.min(score, maxScore)
}