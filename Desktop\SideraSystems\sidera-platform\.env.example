# Sidera Systems - Proof Engine Environment Configuration

# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Sidera Systems"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# n8n Configuration (Self-hosted workflow engine)
N8N_API_URL=http://localhost:5678
N8N_API_KEY=your_n8n_api_key

# Facebook OAuth (for Social Poster workflow)
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret

# Google OAuth (for Google Business Profile integration)
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# SendGrid (for Review Booster workflow)
SENDGRID_API_KEY=SG.your_sendgrid_api_key

# Twilio (for SMS in Review Booster workflow)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token

# Google Gemini AI (for blog generation and SEO responses)
GOOGLE_AI_API_KEY=your_gemini_api_key

# Email Configuration (for system notifications)
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_USER=resend
SMTP_PASS=your_resend_api_key
FROM_EMAIL=<EMAIL>

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change_this_password

# Security
JWT_SECRET=your_jwt_secret_key_here
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# Development/Production Mode
NODE_ENV=development

# Analytics (optional)
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# File Upload (for blog images, etc.)
UPLOAD_MAX_SIZE=5mb
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp