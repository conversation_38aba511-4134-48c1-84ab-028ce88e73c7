{"name": "sidera-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup:n8n": "node scripts/setup-n8n-workflows.js", "dev:full": "echo 'Starting n8n and Next.js...' && concurrently \"npx n8n\" \"npm run dev\"", "check:env": "node -e \"require('dotenv').config({path:'.env.local'}); console.log('Environment check:', {N8N_API_URL: process.env.N8N_API_URL, SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL?.slice(0,20)+'...'})\""}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.7.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.534.0", "next": "15.4.5", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "recharts": "^3.1.0", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}