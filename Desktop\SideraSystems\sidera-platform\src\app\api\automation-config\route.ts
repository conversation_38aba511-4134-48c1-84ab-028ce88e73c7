import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { n8nApi } from '@/lib/n8n'

// GET: Fetch user's automation configurations
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { data: automationConfigs, error } = await supabase
      .from('automation_configs')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch automation configurations' },
        { status: 500 }
      )
    }

    return NextResponse.json({ automationConfigs })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST: Create or update automation configuration
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      automationType, 
      cronSchedule, 
      isActive, 
      settings 
    } = body

    // Validate required fields
    if (!automationType || !cronSchedule) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate cron expression (basic validation)
    if (!isValidCronExpression(cronSchedule)) {
      return NextResponse.json(
        { error: 'Invalid cron expression' },
        { status: 400 }
      )
    }

    // Get existing automation config
    const { data: existingConfig } = await supabase
      .from('automation_configs')
      .select('*')
      .eq('user_id', user.id)
      .eq('automation_type', automationType)
      .single()

    let workflowId = existingConfig?.workflow_id

    // If this is a new automation or workflow doesn't exist, create n8n workflow
    if (!workflowId) {
      try {
        workflowId = await createN8nWorkflow(user.id, automationType, cronSchedule, settings)
      } catch (error) {
        console.error('Failed to create n8n workflow:', error)
        return NextResponse.json(
          { error: 'Failed to create workflow' },
          { status: 500 }
        )
      }
    } else if (existingConfig) {
      // Update existing n8n workflow
      try {
        await updateN8nWorkflow(workflowId, cronSchedule, isActive, settings)
      } catch (error) {
        console.error('Failed to update n8n workflow:', error)
        return NextResponse.json(
          { error: 'Failed to update workflow' },
          { status: 500 }
        )
      }
    }

    // Calculate next run time
    const nextRun = calculateNextRun(cronSchedule)

    // Upsert automation config
    const automationConfig = {
      user_id: user.id,
      automation_type: automationType,
      is_active: isActive !== false, // default to true
      cron_schedule: cronSchedule,
      workflow_id: workflowId,
      settings: settings || {},
      next_run: nextRun,
      updated_at: new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('automation_configs')
      .upsert(automationConfig, {
        onConflict: 'user_id,automation_type'
      })
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to save automation configuration' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true,
      config: data
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT: Update automation status (activate/deactivate)
export async function PUT(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { automationId, isActive } = body

    if (!automationId || typeof isActive !== 'boolean') {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get automation config
    const { data: config, error: configError } = await supabase
      .from('automation_configs')
      .select('*')
      .eq('id', automationId)
      .eq('user_id', user.id) // Ensure user owns this automation
      .single()

    if (configError || !config) {
      return NextResponse.json(
        { error: 'Automation not found' },
        { status: 404 }
      )
    }

    // Update n8n workflow status
    if (config.workflow_id) {
      try {
        if (isActive) {
          await n8nApi.activateWorkflow(config.workflow_id)
        } else {
          await n8nApi.deactivateWorkflow(config.workflow_id)
        }
      } catch (error) {
        console.error('Failed to update n8n workflow status:', error)
        return NextResponse.json(
          { error: 'Failed to update workflow status' },
          { status: 500 }
        )
      }
    }

    // Update database
    const { data, error } = await supabase
      .from('automation_configs')
      .update({ 
        is_active: isActive,
        next_run: isActive ? calculateNextRun(config.cron_schedule) : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', automationId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to update automation' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true,
      config: data
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper functions
function isValidCronExpression(cron: string): boolean {
  // Basic cron validation - should have 5 parts
  const parts = cron.trim().split(/\s+/)
  return parts.length === 5
}

function calculateNextRun(cronSchedule: string): string {
  // This is a simplified calculation
  // In production, use a proper cron parser like 'node-cron' or 'cron-parser'
  const now = new Date()
  
  // For demo purposes, just add some time based on common patterns
  if (cronSchedule === '0 9 * * *') { // Daily at 9 AM
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(9, 0, 0, 0)
    return tomorrow.toISOString()
  } else if (cronSchedule === '0 */4 * * *') { // Every 4 hours
    const next = new Date(now)
    next.setHours(next.getHours() + 4, 0, 0, 0)
    return next.toISOString()
  } else if (cronSchedule === '0 10 * * 1') { // Weekly on Monday at 10 AM
    const next = new Date(now)
    next.setDate(next.getDate() + (1 + 7 - next.getDay()) % 7)
    next.setHours(10, 0, 0, 0)
    return next.toISOString()
  }
  
  // Default: add 1 hour
  const next = new Date(now)
  next.setHours(next.getHours() + 1)
  return next.toISOString()
}

async function createN8nWorkflow(
  userId: string, 
  automationType: string, 
  cronSchedule: string, 
  settings: any
): Promise<string> {
  // Create workflow based on automation type
  const workflowTemplate = getWorkflowTemplate(automationType, userId, cronSchedule, settings)
  const workflow = await n8nApi.createWorkflow(workflowTemplate)
  
  // Activate the workflow
  await n8nApi.activateWorkflow(workflow.id)
  
  return workflow.id
}

async function updateN8nWorkflow(
  workflowId: string, 
  cronSchedule: string, 
  isActive: boolean, 
  settings: any
): Promise<void> {
  // Update workflow schedule
  await n8nApi.updateWorkflowSchedule(workflowId, cronSchedule)
  
  // Update activation status
  if (isActive) {
    await n8nApi.activateWorkflow(workflowId)
  } else {
    await n8nApi.deactivateWorkflow(workflowId)
  }
}

function getWorkflowTemplate(
  automationType: string, 
  userId: string, 
  cronSchedule: string, 
  settings: any
) {
  // Return n8n workflow templates based on automation type
  // These would be stored as JSON templates for each workflow type
  const baseTemplate = {
    name: `${automationType}_${userId}`,
    nodes: [
      {
        name: "Schedule Trigger",
        type: "n8n-nodes-base.scheduleTrigger",
        position: [240, 300],
        parameters: {
          rule: {
            interval: [
              {
                field: "cronExpression",
                expression: cronSchedule
              }
            ]
          }
        }
      },
      // Add more nodes based on automation type
    ],
    connections: {},
    settings: {
      executionOrder: "v1"
    }
  }
  
  return baseTemplate
}