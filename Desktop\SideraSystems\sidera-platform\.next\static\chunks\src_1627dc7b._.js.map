{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow-lg hover:shadow-xl transition-all duration-200\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wGACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n        success: \"border-transparent bg-green-600 text-white hover:bg-green-600/80\",\r\n        warning: \"border-transparent bg-yellow-600 text-white hover:bg-yellow-600/80\",\r\n        info: \"border-transparent bg-blue-600 text-white hover:bg-blue-600/80\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2VACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/lib/types/auth.ts"], "sourcesContent": ["// Authentication types for different workflow providers\r\nexport type AuthMethod = 'oauth' | 'api_key' | 'webhook' | 'email_password'\r\n\r\nexport interface BaseAuthConfig {\r\n  id: string\r\n  user_id: string\r\n  workflow_type: 'review_booster' | 'seo_responder' | 'social_poster'\r\n  provider: string\r\n  auth_method: AuthMethod\r\n  status: 'connected' | 'expired' | 'error' | 'pending'\r\n  created_at: string\r\n  updated_at: string\r\n}\r\n\r\n// OAuth configuration (e.g., Facebook, Google)\r\nexport interface OAuthConfig extends BaseAuthConfig {\r\n  auth_method: 'oauth'\r\n  access_token: string\r\n  refresh_token?: string\r\n  expires_at?: string\r\n  scope: string[]\r\n  provider_user_id: string\r\n  provider_username?: string\r\n}\r\n\r\n// API Key configuration (e.g., SendGrid, Twilio)\r\nexport interface ApiKeyConfig extends BaseAuthConfig {\r\n  auth_method: 'api_key'\r\n  api_key: string\r\n  api_secret?: string\r\n  endpoint?: string\r\n  additional_headers?: Record<string, string>\r\n}\r\n\r\n// Webhook configuration (reverse API)\r\nexport interface WebhookConfig extends BaseAuthConfig {\r\n  auth_method: 'webhook'\r\n  webhook_url: string\r\n  webhook_secret: string\r\n  events: string[]\r\n}\r\n\r\n// Email/Password configuration\r\nexport interface EmailPasswordConfig extends BaseAuthConfig {\r\n  auth_method: 'email_password'\r\n  email: string\r\n  password: string // encrypted\r\n  smtp_server?: string\r\n  smtp_port?: number\r\n}\r\n\r\nexport type WorkflowAuthConfig = OAuthConfig | ApiKeyConfig | WebhookConfig | EmailPasswordConfig\r\n\r\n// Workflow provider definitions\r\nexport interface WorkflowProvider {\r\n  id: string\r\n  name: string\r\n  description: string\r\n  workflow_types: string[]\r\n  auth_method: AuthMethod\r\n  required_scopes?: string[]\r\n  auth_url?: string\r\n  token_url?: string\r\n  client_id?: string\r\n  setup_instructions: string\r\n  icon: string\r\n}\r\n\r\n// ONLY TWO WORKFLOWS SUPPORTED\r\nexport const WORKFLOW_PROVIDERS: Record<string, WorkflowProvider[]> = {\r\n  // 1. SEO Responder - Google review management via AI replies with SEO\r\n  seo_responder: [\r\n    {\r\n      id: 'google_business',\r\n      name: 'Google Business Profile',\r\n      description: 'Connect to manage and respond to Google Business reviews with AI',\r\n      workflow_types: ['seo_responder'],\r\n      auth_method: 'api_key', // Client enters their Google Business API key\r\n      setup_instructions: 'Enter your Google Business API key from Google Cloud Console. We will use AI to generate SEO-optimized responses to your reviews.',\r\n      icon: 'star'\r\n    }\r\n  ],\r\n  \r\n  // 2. Social Poster - Facebook business page posting\r\n  social_poster: [\r\n    {\r\n      id: 'facebook',\r\n      name: 'Facebook Business Pages',\r\n      description: 'Connect your Facebook business page for automated posting',\r\n      workflow_types: ['social_poster'],\r\n      auth_method: 'oauth', // OAuth to OUR Facebook app\r\n      required_scopes: ['pages_manage_posts', 'pages_read_engagement', 'pages_show_list'],\r\n      auth_url: 'https://www.facebook.com/v18.0/dialog/oauth',\r\n      setup_instructions: 'Connect your Facebook business page to automatically post educational content and updates.',\r\n      icon: 'facebook'\r\n    }\r\n  ]\r\n}\r\n\r\nexport interface WorkflowAuthState {\r\n  [workflowType: string]: {\r\n    [providerId: string]: WorkflowAuthConfig | null\r\n  }\r\n}"], "names": [], "mappings": "AAAA,wDAAwD;;;;AAqEjD,MAAM,qBAAyD;IACpE,sEAAsE;IACtE,eAAe;QACb;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,gBAAgB;gBAAC;aAAgB;YACjC,aAAa;YACb,oBAAoB;YACpB,MAAM;QACR;KACD;IAED,oDAAoD;IACpD,eAAe;QACb;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,gBAAgB;gBAAC;aAAgB;YACjC,aAAa;YACb,iBAAiB;gBAAC;gBAAsB;gBAAyB;aAAkB;YACnF,UAAU;YACV,oBAAoB;YACpB,MAAM;QACR;KACD;AACH", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/components/workflow-auth.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { WORKFLOW_PROVIDERS, type WorkflowAuthConfig } from \"@/lib/types/auth\"\r\nimport { \r\n  Facebook, \r\n  Mail, \r\n  MessageSquare, \r\n  Star, \r\n  Building, \r\n  Key, \r\n  CheckCircle, \r\n  AlertCircle, \r\n  ExternalLink,\r\n  Loader2\r\n} from \"lucide-react\"\r\n\r\ninterface WorkflowAuthProps {\r\n  workflowType: 'review_booster' | 'seo_responder' | 'social_poster'\r\n  authConfigs: WorkflowAuthConfig[]\r\n  onAuthConnect: (provider: string, authMethod: string, data: any) => Promise<void>\r\n  onAuthDisconnect: (configId: string) => Promise<void>\r\n}\r\n\r\nconst getProviderIcon = (providerId: string) => {\r\n  switch (providerId) {\r\n    case 'facebook': return <Facebook className=\"h-5 w-5\" />\r\n    case 'google_business': return <Building className=\"h-5 w-5\" />\r\n    case 'sendgrid': return <Mail className=\"h-5 w-5\" />\r\n    case 'twilio': return <MessageSquare className=\"h-5 w-5\" />\r\n    default: return <Key className=\"h-5 w-5\" />\r\n  }\r\n}\r\n\r\nconst getStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case 'connected': return 'success'\r\n    case 'expired': return 'warning'\r\n    case 'error': return 'destructive'\r\n    case 'pending': return 'secondary'\r\n    default: return 'secondary'\r\n  }\r\n}\r\n\r\nexport function WorkflowAuth({ workflowType, authConfigs, onAuthConnect, onAuthDisconnect }: WorkflowAuthProps) {\r\n  const [loadingProvider, setLoadingProvider] = useState<string | null>(null)\r\n  const [apiKeyInputs, setApiKeyInputs] = useState<Record<string, string>>({})\r\n  \r\n  const providers = WORKFLOW_PROVIDERS[workflowType] || []\r\n  \r\n  const getAuthConfig = (providerId: string) => {\r\n    return authConfigs.find(config => config.provider === providerId)\r\n  }\r\n\r\n  const handleOAuthConnect = async (providerId: string) => {\r\n    setLoadingProvider(providerId)\r\n    try {\r\n      // Initiate OAuth flow\r\n      const provider = providers.find(p => p.id === providerId)\r\n      if (!provider) throw new Error('Provider not found')\r\n      \r\n      // Build OAuth URL\r\n      const params = new URLSearchParams({\r\n        client_id: provider.client_id || process.env.NEXT_PUBLIC_OAUTH_CLIENT_ID || '',\r\n        response_type: 'code',\r\n        scope: provider.required_scopes?.join(' ') || '',\r\n        redirect_uri: `${window.location.origin}/api/auth/callback/${providerId}`,\r\n        state: JSON.stringify({ workflowType, providerId })\r\n      })\r\n      \r\n      const authUrl = `${provider.auth_url}?${params.toString()}`\r\n      \r\n      // Open OAuth popup\r\n      const popup = window.open(authUrl, 'oauth', 'width=600,height=600')\r\n      \r\n      // Listen for completion\r\n      const checkClosed = setInterval(() => {\r\n        if (popup?.closed) {\r\n          clearInterval(checkClosed)\r\n          setLoadingProvider(null)\r\n          // Refresh auth configs\r\n          window.location.reload()\r\n        }\r\n      }, 1000)\r\n      \r\n    } catch (error) {\r\n      console.error('OAuth error:', error)\r\n      setLoadingProvider(null)\r\n    }\r\n  }\r\n\r\n  const handleApiKeyConnect = async (providerId: string) => {\r\n    const apiKey = apiKeyInputs[providerId]\r\n    if (!apiKey) return\r\n\r\n    setLoadingProvider(providerId)\r\n    try {\r\n      await onAuthConnect(providerId, 'api_key', { api_key: apiKey })\r\n      setApiKeyInputs(prev => ({ ...prev, [providerId]: '' }))\r\n    } catch (error) {\r\n      console.error('API key connection error:', error)\r\n    } finally {\r\n      setLoadingProvider(null)\r\n    }\r\n  }\r\n\r\n  const handleDisconnect = async (configId: string) => {\r\n    try {\r\n      await onAuthDisconnect(configId)\r\n    } catch (error) {\r\n      console.error('Disconnect error:', error)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold mb-2\">Connect Your Accounts</h3>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Connect the services you want to automate for this workflow.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"grid gap-4\">\r\n        {providers.map((provider) => {\r\n          const authConfig = getAuthConfig(provider.id)\r\n          const isConnected = authConfig?.status === 'connected'\r\n          const isLoading = loadingProvider === provider.id\r\n\r\n          return (\r\n            <Card key={provider.id} className=\"border-0 bg-card/50 backdrop-blur\">\r\n              <CardHeader className=\"pb-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <div className=\"p-2 rounded-lg bg-secondary/50\">\r\n                      {getProviderIcon(provider.id)}\r\n                    </div>\r\n                    <div>\r\n                      <CardTitle className=\"text-base\">{provider.name}</CardTitle>\r\n                      <CardDescription className=\"text-sm\">\r\n                        {provider.description}\r\n                      </CardDescription>\r\n                    </div>\r\n                  </div>\r\n                  {authConfig && (\r\n                    <Badge variant={getStatusColor(authConfig.status) as any}>\r\n                      {authConfig.status === 'connected' && <CheckCircle className=\"h-3 w-3 mr-1\" />}\r\n                      {authConfig.status === 'error' && <AlertCircle className=\"h-3 w-3 mr-1\" />}\r\n                      {authConfig.status}\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </CardHeader>\r\n\r\n              <CardContent>\r\n                <p className=\"text-sm text-muted-foreground mb-4\">\r\n                  {provider.setup_instructions}\r\n                </p>\r\n\r\n                {isConnected ? (\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center gap-2 text-sm text-green-400\">\r\n                      <CheckCircle className=\"h-4 w-4\" />\r\n                      Connected{authConfig.provider_username && ` as ${authConfig.provider_username}`}\r\n                    </div>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => handleDisconnect(authConfig.id)}\r\n                    >\r\n                      Disconnect\r\n                    </Button>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-3\">\r\n                    {provider.auth_method === 'oauth' ? (\r\n                      <Button\r\n                        onClick={() => handleOAuthConnect(provider.id)}\r\n                        disabled={isLoading}\r\n                        className=\"w-full\"\r\n                        variant=\"outline\"\r\n                      >\r\n                        {isLoading ? (\r\n                          <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                        ) : (\r\n                          <ExternalLink className=\"h-4 w-4 mr-2\" />\r\n                        )}\r\n                        Connect {provider.name}\r\n                      </Button>\r\n                    ) : (\r\n                      <div className=\"flex gap-2\">\r\n                        <Input\r\n                          placeholder=\"Enter API key...\"\r\n                          type=\"password\"\r\n                          value={apiKeyInputs[provider.id] || ''}\r\n                          onChange={(e) => setApiKeyInputs(prev => ({\r\n                            ...prev,\r\n                            [provider.id]: e.target.value\r\n                          }))}\r\n                        />\r\n                        <Button\r\n                          onClick={() => handleApiKeyConnect(provider.id)}\r\n                          disabled={isLoading || !apiKeyInputs[provider.id]}\r\n                        >\r\n                          {isLoading ? (\r\n                            <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                          ) : (\r\n                            'Connect'\r\n                          )}\r\n                        </Button>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {authConfig?.status === 'error' && (\r\n                      <div className=\"text-sm text-destructive\">\r\n                        Connection failed. Please try again.\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          )\r\n        })}\r\n      </div>\r\n\r\n      {providers.length === 0 && (\r\n        <Card className=\"border-0 bg-card/50 backdrop-blur\">\r\n          <CardContent className=\"text-center py-8\">\r\n            <p className=\"text-muted-foreground\">\r\n              No providers configured for this workflow type.\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;AAmEyC;;AAjEzC;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AA4BA,MAAM,kBAAkB,CAAC;IACvB,OAAQ;QACN,KAAK;YAAY,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5C,KAAK;YAAmB,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QACnD,KAAK;YAAY,qBAAO,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;QACxC,KAAK;YAAU,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAC/C;YAAS,qBAAO,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;IACjC;AACF;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YAAa,OAAO;QACzB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAW,OAAO;QACvB;YAAS,OAAO;IAClB;AACF;AAEO,SAAS,aAAa,KAAiF;QAAjF,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAqB,GAAjF;;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE1E,MAAM,YAAY,8HAAA,CAAA,qBAAkB,CAAC,aAAa,IAAI,EAAE;IAExD,MAAM,gBAAgB,CAAC;QACrB,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IACxD;IAEA,MAAM,qBAAqB,OAAO;QAChC,mBAAmB;QACnB,IAAI;gBASO;YART,sBAAsB;YACtB,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC9C,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;YAE/B,kBAAkB;YAClB,MAAM,SAAS,IAAI,gBAAgB;gBACjC,WAAW,SAAS,SAAS,IAAI,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI;gBAC5E,eAAe;gBACf,OAAO,EAAA,4BAAA,SAAS,eAAe,cAAxB,gDAAA,0BAA0B,IAAI,CAAC,SAAQ;gBAC9C,cAAc,AAAC,GAA8C,OAA5C,OAAO,QAAQ,CAAC,MAAM,EAAC,uBAAgC,OAAX;gBAC7D,OAAO,KAAK,SAAS,CAAC;oBAAE;oBAAc;gBAAW;YACnD;YAEA,MAAM,UAAU,AAAC,GAAuB,OAArB,SAAS,QAAQ,EAAC,KAAqB,OAAlB,OAAO,QAAQ;YAEvD,mBAAmB;YACnB,MAAM,QAAQ,OAAO,IAAI,CAAC,SAAS,SAAS;YAE5C,wBAAwB;YACxB,MAAM,cAAc,YAAY;gBAC9B,IAAI,kBAAA,4BAAA,MAAO,MAAM,EAAE;oBACjB,cAAc;oBACd,mBAAmB;oBACnB,uBAAuB;oBACvB,OAAO,QAAQ,CAAC,MAAM;gBACxB;YACF,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,mBAAmB;QACrB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,MAAM,SAAS,YAAY,CAAC,WAAW;QACvC,IAAI,CAAC,QAAQ;QAEb,mBAAmB;QACnB,IAAI;YACF,MAAM,cAAc,YAAY,WAAW;gBAAE,SAAS;YAAO;YAC7D,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,WAAW,EAAE;gBAAG,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,iBAAiB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;oBACd,MAAM,aAAa,cAAc,SAAS,EAAE;oBAC5C,MAAM,cAAc,CAAA,uBAAA,iCAAA,WAAY,MAAM,MAAK;oBAC3C,MAAM,YAAY,oBAAoB,SAAS,EAAE;oBAEjD,qBACE,6LAAC,mIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,gBAAgB,SAAS,EAAE;;;;;;8DAE9B,6LAAC;;sEACC,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAa,SAAS,IAAI;;;;;;sEAC/C,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,SAAS,WAAW;;;;;;;;;;;;;;;;;;wCAI1B,4BACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAS,eAAe,WAAW,MAAM;;gDAC7C,WAAW,MAAM,KAAK,6BAAe,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAC5D,WAAW,MAAM,KAAK,yBAAW,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACxD,WAAW,MAAM;;;;;;;;;;;;;;;;;;0CAM1B,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAE,WAAU;kDACV,SAAS,kBAAkB;;;;;;oCAG7B,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAY;oDACzB,WAAW,iBAAiB,IAAI,AAAC,OAAmC,OAA7B,WAAW,iBAAiB;;;;;;;0DAE/E,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB,WAAW,EAAE;0DAC9C;;;;;;;;;;;6DAKH,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,WAAW,KAAK,wBACxB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,mBAAmB,SAAS,EAAE;gDAC7C,UAAU;gDACV,WAAU;gDACV,SAAQ;;oDAEP,0BACC,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACxB;oDACO,SAAS,IAAI;;;;;;qEAGxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,MAAK;wDACL,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI;wDACpC,UAAU,CAAC,IAAM,gBAAgB,CAAA,OAAQ,CAAC;oEACxC,GAAG,IAAI;oEACP,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;gEAC/B,CAAC;;;;;;kEAEH,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;wDAC9C,UAAU,aAAa,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;kEAEhD,0BACC,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;mEAEnB;;;;;;;;;;;;4CAMP,CAAA,uBAAA,iCAAA,WAAY,MAAM,MAAK,yBACtB,6LAAC;gDAAI,WAAU;0DAA2B;;;;;;;;;;;;;;;;;;;uBApFzC,SAAS,EAAE;;;;;gBA6F1B;;;;;;YAGD,UAAU,MAAM,KAAK,mBACpB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAjMgB;KAAA", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SideraSystems/sidera-platform/src/app/onboarding/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Input } from \"@/components/ui/input\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { WorkflowAuth } from \"@/components/workflow-auth\"\nimport { \n  CheckCircle, \n  ArrowRight,\n  Star,\n  MessageSquare,\n  Share2,\n  Clock,\n  Settings,\n  Sparkles\n} from \"lucide-react\"\n\nconst automationOptions = [\n  {\n    id: 'review_booster',\n    name: 'Review Booster',\n    description: 'Automatically request reviews from satisfied customers',\n    icon: <Star className=\"h-6 w-6\" />,\n    color: 'text-green-400',\n    popular: true,\n    scheduleOptions: [\n      { label: 'After each job completion', value: '0 9 * * *' },\n      { label: 'Weekly on Mondays', value: '0 9 * * 1' },\n      { label: 'Bi-weekly', value: '0 9 */14 * *' }\n    ]\n  },\n  {\n    id: 'seo_responder',\n    name: '<PERSON><PERSON>sponder',\n    description: 'AI-powered responses to Google Business reviews',\n    icon: <MessageSquare className=\"h-6 w-6\" />,\n    color: 'text-blue-400',\n    popular: false,\n    scheduleOptions: [\n      { label: 'Every 4 hours', value: '0 */4 * * *' },\n      { label: 'Twice daily', value: '0 9,17 * * *' },\n      { label: 'Daily at 9 AM', value: '0 9 * * *' }\n    ]\n  },\n  {\n    id: 'social_poster',\n    name: 'Social Poster',\n    description: 'Automated educational content posting',\n    icon: <Share2 className=\"h-6 w-6\" />,\n    color: 'text-purple-400',\n    popular: false,\n    scheduleOptions: [\n      { label: 'Weekly on Monday', value: '0 10 * * 1' },\n      { label: 'Bi-weekly', value: '0 10 */14 * *' },\n      { label: 'Monthly', value: '0 10 1 * *' }\n    ]\n  }\n]\n\nexport default function Onboarding() {\n  const [step, setStep] = useState(1)\n  const [selectedAutomations, setSelectedAutomations] = useState<string[]>(['review_booster'])\n  const [automationSettings, setAutomationSettings] = useState<Record<string, any>>({})\n  const [authConfigs, setAuthConfigs] = useState([])\n\n  const handleAutomationToggle = (automationId: string) => {\n    setSelectedAutomations(prev => \n      prev.includes(automationId)\n        ? prev.filter(id => id !== automationId)\n        : [...prev, automationId]\n    )\n  }\n\n  const handleScheduleChange = (automationId: string, schedule: string) => {\n    setAutomationSettings(prev => ({\n      ...prev,\n      [automationId]: { ...prev[automationId], schedule }\n    }))\n  }\n\n  const handleAuthConnect = async (provider: string, authMethod: string, data: any) => {\n    console.log('Connect auth:', { provider, authMethod, data })\n    // Here we would call our API to save the auth config\n  }\n\n  const handleAuthDisconnect = async (configId: string) => {\n    console.log('Disconnect auth:', configId)\n    // Here we would call our API to remove the auth config\n  }\n\n  const handleComplete = () => {\n    console.log('Onboarding complete:', {\n      selectedAutomations,\n      automationSettings,\n      authConfigs\n    })\n    // Save settings and redirect to dashboard\n    window.location.href = '/dashboard'\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <div className=\"container mx-auto py-8 px-4 max-w-4xl\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <Link href=\"/\" className=\"flex items-center justify-center space-x-3 mb-8\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent\">\n                Proof Engine\n              </div>\n              <div className=\"text-sm text-slate-400 font-medium\">\n                by Sidera Systems\n              </div>\n            </div>\n          </Link>\n          \n          <div className=\"space-y-4 mb-6\">\n            <div className=\"flex items-center justify-center space-x-2\">\n              <Sparkles className=\"h-5 w-5 text-amber-400\" />\n              <h1 className=\"text-4xl font-bold bg-gradient-to-r from-white via-slate-100 to-slate-300 bg-clip-text text-transparent\">\n                Welcome aboard\n              </h1>\n              <Sparkles className=\"h-5 w-5 text-amber-400\" />\n            </div>\n            \n            <p className=\"text-lg text-slate-400 max-w-xl mx-auto leading-relaxed\">\n              Let's build your AI marketing engine. This setup takes just 3 minutes and will transform how you connect with customers.\n            </p>\n          </div>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"flex items-center justify-center space-x-6 mb-12\">\n          {[\n            { step: 1, label: 'Choose Automations' },\n            { step: 2, label: 'Configure Settings' },\n            { step: 3, label: 'Connect Accounts' }\n          ].map(({ step: stepNum, label }) => (\n            <div key={stepNum} className=\"flex items-center\">\n              <div className={`relative w-10 h-10 rounded-xl flex items-center justify-center text-sm font-semibold transition-all duration-300 ${\n                step >= stepNum \n                  ? 'bg-gradient-to-br from-emerald-500 to-emerald-600 text-white shadow-lg shadow-emerald-500/25 border border-emerald-400/50' \n                  : 'bg-slate-800/50 text-slate-400 border border-slate-700/50'\n              }`}>\n                {step > stepNum ? (\n                  <CheckCircle className=\"h-5 w-5\" />\n                ) : (\n                  <span className=\"text-sm font-bold\">{stepNum}</span>\n                )}\n                {step >= stepNum && (\n                  <div className=\"absolute inset-0 rounded-xl bg-gradient-to-br from-emerald-400/20 to-transparent animate-pulse\" />\n                )}\n              </div>\n              <span className={`ml-3 text-sm font-medium transition-colors ${\n                step >= stepNum ? 'text-slate-200' : 'text-slate-500'\n              }`}>\n                {label}\n              </span>\n              {stepNum < 3 && (\n                <ArrowRight className={`h-4 w-4 mx-6 transition-colors ${\n                  step >= stepNum ? 'text-slate-400' : 'text-slate-600'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Step 1: Choose Automations */}\n        {step === 1 && (\n          <div className=\"space-y-8\">\n            <div className=\"text-center\">\n              <h2 className=\"text-3xl font-bold mb-4 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent\">\n                Choose Your Automations\n              </h2>\n              <p className=\"text-slate-400 text-lg max-w-2xl mx-auto leading-relaxed\">\n                Select the AI-powered automations that will transform your marketing. Each automation runs independently on your schedule.\n              </p>\n            </div>\n\n            <div className=\"grid gap-6 max-w-4xl mx-auto\">\n              {automationOptions.map((automation) => (\n                <Card \n                  key={automation.id}\n                  className={`group relative overflow-hidden border-0 cursor-pointer transition-all duration-500 transform ${\n                    selectedAutomations.includes(automation.id)\n                      ? 'scale-[1.02] shadow-2xl shadow-emerald-500/20 bg-gradient-to-br from-slate-800/90 via-slate-800/80 to-emerald-900/20 ring-2 ring-emerald-500/50'\n                      : 'hover:scale-[1.01] hover:shadow-xl hover:shadow-slate-900/50 bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 border border-slate-700/50'\n                  }`}\n                  onClick={() => handleAutomationToggle(automation.id)}\n                >\n                                    {/* Background overlay */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900/20 via-transparent to-slate-900/40 pointer-events-none\" />\n                  \n                  <CardHeader className=\"relative pb-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-5\">\n                        <div className={`relative p-4 rounded-2xl bg-gradient-to-br from-slate-700/80 to-slate-800/80 backdrop-blur border border-slate-600/50 shadow-lg ${automation.color}`}>\n                          {automation.icon}\n                          <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 to-transparent pointer-events-none\" />\n                        </div>\n                        <div className=\"space-y-2\">\n                          <div className=\"flex items-center gap-3\">\n                            <CardTitle className=\"text-xl font-bold text-slate-100\">\n                              {automation.name}\n                            </CardTitle>\n                            {automation.popular && (\n                              <Badge className=\"bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-lg\">\n                                Most Popular\n                              </Badge>\n                            )}\n                          </div>\n                          <CardDescription className=\"text-slate-400 text-base leading-relaxed\">\n                            {automation.description}\n                          </CardDescription>\n                        </div>\n                      </div>\n                      <div className={`relative w-8 h-8 rounded-xl border-2 flex items-center justify-center transition-all duration-300 ${\n                        selectedAutomations.includes(automation.id)\n                          ? 'border-emerald-400 bg-gradient-to-br from-emerald-500 to-emerald-600 shadow-lg shadow-emerald-500/25'\n                          : 'border-slate-600 bg-slate-800/50 group-hover:border-slate-500'\n                      }`}>\n                        {selectedAutomations.includes(automation.id) && (\n                          <CheckCircle className=\"h-5 w-5 text-white\" />\n                        )}\n                      </div>\n                    </div>\n                  </CardHeader>\n                </Card>\n              ))}\n            </div>\n\n            <div className=\"text-center pt-4\">\n              <Button \n                onClick={() => setStep(2)}\n                size=\"lg\"\n                disabled={selectedAutomations.length === 0}\n                className=\"px-8 py-4 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white font-semibold rounded-xl shadow-lg shadow-emerald-600/25 border-0 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-emerald-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n              >\n                <span className=\"text-lg\">\n                  Continue with {selectedAutomations.length} automation{selectedAutomations.length !== 1 ? 's' : ''}\n                </span>\n                <ArrowRight className=\"ml-3 h-5 w-5\" />\n              </Button>\n              \n              {selectedAutomations.length === 0 && (\n                <p className=\"text-slate-500 text-sm mt-3\">\n                  Select at least one automation to continue\n                </p>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Step 2: Configure Settings */}\n        {step === 2 && (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-semibold mb-2\">Configure Automation Settings</h2>\n              <p className=\"text-muted-foreground\">\n                Set up schedules and preferences for your selected automations.\n              </p>\n            </div>\n\n            <div className=\"space-y-4 max-w-3xl mx-auto\">\n              {selectedAutomations.map((automationId) => {\n                const automation = automationOptions.find(a => a.id === automationId)!\n                return (\n                  <Card key={automationId} className=\"border-0 bg-card/50 backdrop-blur\">\n                    <CardHeader className=\"pb-4\">\n                      <div className=\"flex items-center gap-4\">\n                        <div className={`p-3 rounded-lg bg-secondary/50 ${automation.color}`}>\n                          {automation.icon}\n                        </div>\n                        <div>\n                          <CardTitle className=\"text-lg\">{automation.name}</CardTitle>\n                          <CardDescription>Configure when this automation runs</CardDescription>\n                        </div>\n                      </div>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"text-sm font-medium mb-3 block\">\n                            <Clock className=\"h-4 w-4 inline mr-2\" />\n                            Schedule\n                          </label>\n                          <div className=\"grid gap-2\">\n                            {automation.scheduleOptions.map((option) => (\n                              <label\n                                key={option.value}\n                                className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${\n                                  automationSettings[automationId]?.schedule === option.value\n                                    ? 'border-brand-600 bg-brand-600/5'\n                                    : 'border-border hover:border-brand-600/50'\n                                }`}\n                              >\n                                <input\n                                  type=\"radio\"\n                                  name={`schedule-${automationId}`}\n                                  value={option.value}\n                                  checked={automationSettings[automationId]?.schedule === option.value}\n                                  onChange={() => handleScheduleChange(automationId, option.value)}\n                                  className=\"sr-only\"\n                                />\n                                <div className={`w-4 h-4 rounded-full border-2 mr-3 ${\n                                  automationSettings[automationId]?.schedule === option.value\n                                    ? 'border-brand-600 bg-brand-600'\n                                    : 'border-muted'\n                                }`}>\n                                  {automationSettings[automationId]?.schedule === option.value && (\n                                    <div className=\"w-full h-full rounded-full bg-white scale-50\" />\n                                  )}\n                                </div>\n                                <span className=\"text-sm\">{option.label}</span>\n                              </label>\n                            ))}\n                          </div>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                )\n              })}\n            </div>\n\n            <div className=\"text-center space-x-4\">\n              <Button variant=\"outline\" onClick={() => setStep(1)}>\n                Back\n              </Button>\n              <Button onClick={() => setStep(3)} size=\"lg\">\n                Continue to Account Setup\n                <ArrowRight className=\"ml-2 h-5 w-5\" />\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {/* Step 3: Connect Accounts */}\n        {step === 3 && (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-semibold mb-2\">Connect Your Accounts</h2>\n              <p className=\"text-muted-foreground\">\n                Connect the services and platforms your automations will use.\n              </p>\n            </div>\n\n            <div className=\"max-w-3xl mx-auto\">\n              {selectedAutomations.map((automationId) => (\n                <div key={automationId} className=\"mb-8\">\n                  <h3 className=\"text-lg font-semibold mb-4 flex items-center gap-2\">\n                    {automationOptions.find(a => a.id === automationId)?.icon}\n                    {automationOptions.find(a => a.id === automationId)?.name}\n                  </h3>\n                  <WorkflowAuth\n                    workflowType={automationId as any}\n                    authConfigs={authConfigs}\n                    onAuthConnect={handleAuthConnect}\n                    onAuthDisconnect={handleAuthDisconnect}\n                  />\n                </div>\n              ))}\n            </div>\n\n            <div className=\"text-center space-x-4\">\n              <Button variant=\"outline\" onClick={() => setStep(2)}>\n                Back\n              </Button>\n              <Button onClick={handleComplete} size=\"lg\">\n                Complete Setup\n                <CheckCircle className=\"ml-2 h-5 w-5\" />\n              </Button>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AAoBA,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,OAAO;QACP,SAAS;QACT,iBAAiB;YACf;gBAAE,OAAO;gBAA6B,OAAO;YAAY;YACzD;gBAAE,OAAO;gBAAqB,OAAO;YAAY;YACjD;gBAAE,OAAO;gBAAa,OAAO;YAAe;SAC7C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/B,OAAO;QACP,SAAS;QACT,iBAAiB;YACf;gBAAE,OAAO;gBAAiB,OAAO;YAAc;YAC/C;gBAAE,OAAO;gBAAe,OAAO;YAAe;YAC9C;gBAAE,OAAO;gBAAiB,OAAO;YAAY;SAC9C;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,6LAAC,6MAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,OAAO;QACP,SAAS;QACT,iBAAiB;YACf;gBAAE,OAAO;gBAAoB,OAAO;YAAa;YACjD;gBAAE,OAAO;gBAAa,OAAO;YAAgB;YAC7C;gBAAE,OAAO;gBAAW,OAAO;YAAa;SACzC;IACH;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAiB;IAC3F,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEjD,MAAM,yBAAyB,CAAC;QAC9B,uBAAuB,CAAA,OACrB,KAAK,QAAQ,CAAC,gBACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,gBACzB;mBAAI;gBAAM;aAAa;IAE/B;IAEA,MAAM,uBAAuB,CAAC,cAAsB;QAClD,sBAAsB,CAAA,OAAQ,CAAC;gBAC7B,GAAG,IAAI;gBACP,CAAC,aAAa,EAAE;oBAAE,GAAG,IAAI,CAAC,aAAa;oBAAE;gBAAS;YACpD,CAAC;IACH;IAEA,MAAM,oBAAoB,OAAO,UAAkB,YAAoB;QACrE,QAAQ,GAAG,CAAC,iBAAiB;YAAE;YAAU;YAAY;QAAK;IAC1D,qDAAqD;IACvD;IAEA,MAAM,uBAAuB,OAAO;QAClC,QAAQ,GAAG,CAAC,oBAAoB;IAChC,uDAAuD;IACzD;IAEA,MAAM,iBAAiB;QACrB,QAAQ,GAAG,CAAC,wBAAwB;YAClC;YACA;YACA;QACF;QACA,0CAA0C;QAC1C,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA4F;;;;;;kDAG3G,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;sCAMxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAG,WAAU;sDAA0G;;;;;;sDAGxH,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAGtB,6LAAC;oCAAE,WAAU;8CAA0D;;;;;;;;;;;;;;;;;;8BAO3E,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,MAAM;4BAAG,OAAO;wBAAqB;wBACvC;4BAAE,MAAM;4BAAG,OAAO;wBAAqB;wBACvC;4BAAE,MAAM;4BAAG,OAAO;wBAAmB;qBACtC,CAAC,GAAG,CAAC;4BAAC,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE;6CAC7B,6LAAC;4BAAkB,WAAU;;8CAC3B,6LAAC;oCAAI,WAAW,AAAC,oHAIhB,OAHC,QAAQ,UACJ,8HACA;;wCAEH,OAAO,wBACN,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;wCAEtC,QAAQ,yBACP,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGnB,6LAAC;oCAAK,WAAW,AAAC,8CAEjB,OADC,QAAQ,UAAU,mBAAmB;8CAEpC;;;;;;gCAEF,UAAU,mBACT,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAW,AAAC,kCAEvB,OADC,QAAQ,UAAU,mBAAmB;;;;;;;2BAtBjC;;;;;;;;;;;gBA8Bb,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiG;;;;;;8CAG/G,6LAAC;oCAAE,WAAU;8CAA2D;;;;;;;;;;;;sCAK1E,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,6LAAC,mIAAA,CAAA,OAAI;oCAEH,WAAW,AAAC,gGAIX,OAHC,oBAAoB,QAAQ,CAAC,WAAW,EAAE,IACtC,oJACA;oCAEN,SAAS,IAAM,uBAAuB,WAAW,EAAE;;sDAGnD,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,mIAAmJ,OAAjB,WAAW,KAAK;;oEAChK,WAAW,IAAI;kFAChB,6LAAC;wEAAI,WAAU;;;;;;;;;;;;0EAEjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,mIAAA,CAAA,YAAS;gFAAC,WAAU;0FAClB,WAAW,IAAI;;;;;;4EAEjB,WAAW,OAAO,kBACjB,6LAAC,oIAAA,CAAA,QAAK;gFAAC,WAAU;0FAA8E;;;;;;;;;;;;kFAKnG,6LAAC,mIAAA,CAAA,kBAAe;wEAAC,WAAU;kFACxB,WAAW,WAAW;;;;;;;;;;;;;;;;;;kEAI7B,6LAAC;wDAAI,WAAW,AAAC,qGAIhB,OAHC,oBAAoB,QAAQ,CAAC,WAAW,EAAE,IACtC,yGACA;kEAEH,oBAAoB,QAAQ,CAAC,WAAW,EAAE,mBACzC,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCAxC1B,WAAW,EAAE;;;;;;;;;;sCAiDxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,QAAQ;oCACvB,MAAK;oCACL,UAAU,oBAAoB,MAAM,KAAK;oCACzC,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;;gDAAU;gDACT,oBAAoB,MAAM;gDAAC;gDAAY,oBAAoB,MAAM,KAAK,IAAI,MAAM;;;;;;;sDAEjG,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;gCAGvB,oBAAoB,MAAM,KAAK,mBAC9B,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;gBASlD,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC;gCACxB,MAAM,aAAa,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gCACxD,qBACE,6LAAC,mIAAA,CAAA,OAAI;oCAAoB,WAAU;;sDACjC,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,kCAAkD,OAAjB,WAAW,KAAK;kEAC/D,WAAW,IAAI;;;;;;kEAElB,6LAAC;;0EACC,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAW,WAAW,IAAI;;;;;;0EAC/C,6LAAC,mIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;;;;;;;;;;;;sDAIvB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;8EACf,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAwB;;;;;;;sEAG3C,6LAAC;4DAAI,WAAU;sEACZ,WAAW,eAAe,CAAC,GAAG,CAAC,CAAC;oEAI3B,kCASS,mCAKT,mCAIC;qFArBL,6LAAC;oEAEC,WAAW,AAAC,4EAIX,OAHC,EAAA,mCAAA,kBAAkB,CAAC,aAAa,cAAhC,uDAAA,iCAAkC,QAAQ,MAAK,OAAO,KAAK,GACvD,oCACA;;sFAGN,6LAAC;4EACC,MAAK;4EACL,MAAM,AAAC,YAAwB,OAAb;4EAClB,OAAO,OAAO,KAAK;4EACnB,SAAS,EAAA,oCAAA,kBAAkB,CAAC,aAAa,cAAhC,wDAAA,kCAAkC,QAAQ,MAAK,OAAO,KAAK;4EACpE,UAAU,IAAM,qBAAqB,cAAc,OAAO,KAAK;4EAC/D,WAAU;;;;;;sFAEZ,6LAAC;4EAAI,WAAW,AAAC,sCAIhB,OAHC,EAAA,oCAAA,kBAAkB,CAAC,aAAa,cAAhC,wDAAA,kCAAkC,QAAQ,MAAK,OAAO,KAAK,GACvD,kCACA;sFAEH,EAAA,oCAAA,kBAAkB,CAAC,aAAa,cAAhC,wDAAA,kCAAkC,QAAQ,MAAK,OAAO,KAAK,kBAC1D,6LAAC;gFAAI,WAAU;;;;;;;;;;;sFAGnB,6LAAC;4EAAK,WAAU;sFAAW,OAAO,KAAK;;;;;;;mEAxBlC,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAtBpB;;;;;4BAuDf;;;;;;sCAGF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,QAAQ;8CAAI;;;;;;8CAGrD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,QAAQ;oCAAI,MAAK;;wCAAK;sDAE3C,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;gBAO7B,SAAS,mBACR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC;oCAGnB,yBACA;qDAHL,6LAAC;oCAAuB,WAAU;;sDAChC,6LAAC;4CAAG,WAAU;;iDACX,0BAAA,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,2BAArC,8CAAA,wBAAoD,IAAI;iDACxD,2BAAA,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,2BAArC,+CAAA,yBAAoD,IAAI;;;;;;;sDAE3D,6LAAC,yIAAA,CAAA,eAAY;4CACX,cAAc;4CACd,aAAa;4CACb,eAAe;4CACf,kBAAkB;;;;;;;mCATZ;;;;;;;;;;;sCAed,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,QAAQ;8CAAI;;;;;;8CAGrD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAgB,MAAK;;wCAAK;sDAEzC,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;GA/TwB;KAAA", "debugId": null}}]}