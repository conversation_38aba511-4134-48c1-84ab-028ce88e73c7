import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import Strip<PERSON> from 'stripe'
import { createClient } from '@/lib/supabase/server'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20'
})

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!

// POST /api/webhooks/stripe - Handles billing state updates → updates Supabase
export async function POST(request: NextRequest) {
  const body = await request.text()
  const headersList = await headers()
  const sig = headersList.get('stripe-signature')!

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret)
  } catch (err) {
    console.error('Webhook signature verification failed:', err)
    return NextResponse.json(
      { error: 'Webhook signature verification failed' },
      { status: 400 }
    )
  }

  const supabase = await createClient()

  try {
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription, supabase)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription, supabase)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription, supabase)
        break

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice, supabase)
        break

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice, supabase)
        break

      case 'customer.created':
        await handleCustomerCreated(event.data.object as Stripe.Customer, supabase)
        break

      case 'customer.updated':
        await handleCustomerUpdated(event.data.object as Stripe.Customer, supabase)
        break

      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session, supabase)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

// Handle subscription creation
async function handleSubscriptionCreated(subscription: Stripe.Subscription, supabase: any) {
  const customerId = subscription.customer as string
  const priceId = subscription.items.data[0]?.price.id

  // Map Stripe price IDs to subscription tiers
  const tier = mapPriceIdToTier(priceId)

  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_tier: tier,
      subscription_status: subscription.status === 'active' ? 'active' : subscription.status,
      updated_at: new Date().toISOString()
    })
    .eq('stripe_customer_id', customerId)

  if (error) {
    throw new Error(`Failed to update subscription: ${error.message}`)
  }

  // Log the subscription creation
  const { data: profile } = await supabase
    .from('profiles')
    .select('id, full_name')
    .eq('stripe_customer_id', customerId)
    .single()

  if (profile) {
    await supabase
      .from('logs')
      .insert({
        user_id: profile.id,
        automation_type: 'review_booster', // Default for system events
        event_type: 'success',
        message: `Subscription created: ${tier} plan`,
        details: {
          subscription_id: subscription.id,
          customer_id: customerId,
          tier,
          status: subscription.status
        }
      })
  }

  console.log(`Subscription created for customer ${customerId}: ${tier}`)
}

// Handle subscription updates
async function handleSubscriptionUpdated(subscription: Stripe.Subscription, supabase: any) {
  const customerId = subscription.customer as string
  const priceId = subscription.items.data[0]?.price.id
  const tier = mapPriceIdToTier(priceId)

  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_tier: tier,
      subscription_status: subscription.status === 'active' ? 'active' : 
                          subscription.status === 'past_due' ? 'past_due' :
                          subscription.status === 'canceled' ? 'canceled' : subscription.status,
      updated_at: new Date().toISOString()
    })
    .eq('stripe_customer_id', customerId)

  if (error) {
    throw new Error(`Failed to update subscription: ${error.message}`)
  }

  // If subscription is cancelled or past due, deactivate automations
  if (subscription.status === 'canceled' || subscription.status === 'past_due') {
    await deactivateUserAutomations(customerId, supabase)
  }

  console.log(`Subscription updated for customer ${customerId}: ${tier}, status: ${subscription.status}`)
}

// Handle subscription deletion
async function handleSubscriptionDeleted(subscription: Stripe.Subscription, supabase: any) {
  const customerId = subscription.customer as string

  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'canceled',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_customer_id', customerId)

  if (error) {
    throw new Error(`Failed to update subscription deletion: ${error.message}`)
  }

  // Deactivate all automations for this user
  await deactivateUserAutomations(customerId, supabase)

  console.log(`Subscription deleted for customer ${customerId}`)
}

// Handle successful payment
async function handlePaymentSucceeded(invoice: Stripe.Invoice, supabase: any) {
  const customerId = invoice.customer as string

  // Reactivate automations if they were paused due to payment issues
  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'active',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_customer_id', customerId)

  if (error) {
    console.error('Failed to update payment success:', error)
  }

  console.log(`Payment succeeded for customer ${customerId}`)
}

// Handle failed payment
async function handlePaymentFailed(invoice: Stripe.Invoice, supabase: any) {
  const customerId = invoice.customer as string

  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'past_due',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_customer_id', customerId)

  if (error) {
    console.error('Failed to update payment failure:', error)
  }

  // Deactivate automations due to payment failure
  await deactivateUserAutomations(customerId, supabase)

  console.log(`Payment failed for customer ${customerId}`)
}

// Handle customer creation
async function handleCustomerCreated(customer: Stripe.Customer, supabase: any) {
  // Find user by email and link Stripe customer ID
  const { error } = await supabase
    .from('profiles')
    .update({
      stripe_customer_id: customer.id,
      updated_at: new Date().toISOString()
    })
    .eq('email', customer.email)

  if (error) {
    console.error('Failed to link Stripe customer:', error)
  }

  console.log(`Customer created: ${customer.id} (${customer.email})`)
}

// Handle customer updates
async function handleCustomerUpdated(customer: Stripe.Customer, supabase: any) {
  // Update customer info if needed
  const updateData: any = {
    updated_at: new Date().toISOString()
  }

  if (customer.name) {
    updateData.full_name = customer.name
  }

  const { error } = await supabase
    .from('profiles')
    .update(updateData)
    .eq('stripe_customer_id', customer.id)

  if (error) {
    console.error('Failed to update customer:', error)
  }

  console.log(`Customer updated: ${customer.id}`)
}

// Handle completed checkout session
async function handleCheckoutCompleted(session: Stripe.Checkout.Session, supabase: any) {
  const customerId = session.customer as string
  
  // This is typically where you'd handle the first successful payment
  // and activate the user's account/automations for the first time
  
  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'active',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_customer_id', customerId)

  if (error) {
    console.error('Failed to update checkout completion:', error)
  }

  console.log(`Checkout completed for customer ${customerId}`)
}

// Helper function to map Stripe price IDs to subscription tiers
function mapPriceIdToTier(priceId?: string): 'basic' | 'standard' | 'premium' {
  // These would be your actual Stripe price IDs from your products
  const priceMapping: Record<string, 'basic' | 'standard' | 'premium'> = {
    [process.env.STRIPE_BASIC_PRICE_ID || '']: 'basic',
    [process.env.STRIPE_STANDARD_PRICE_ID || '']: 'standard',
    [process.env.STRIPE_PREMIUM_PRICE_ID || '']: 'premium'
  }

  return priceMapping[priceId || ''] || 'basic'
}

// Helper function to deactivate user automations
async function deactivateUserAutomations(customerId: string, supabase: any) {
  try {
    // Get user ID from customer ID
    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('stripe_customer_id', customerId)
      .single()

    if (!profile) return

    // Deactivate all automations for this user
    const { error } = await supabase
      .from('automation_configs')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', profile.id)

    if (error) {
      console.error('Failed to deactivate automations:', error)
    }

    // Note: In a full implementation, you'd also want to deactivate 
    // the corresponding n8n workflows using the n8n API

    console.log(`Deactivated automations for user ${profile.id}`)
  } catch (error) {
    console.error('Error deactivating automations:', error)
  }
}