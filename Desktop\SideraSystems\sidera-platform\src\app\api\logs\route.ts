import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// GET /api/logs?user_id=... - Returns log events for dashboard
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const requestedUserId = searchParams.get('user_id')
    const automationType = searchParams.get('automation_type')
    const eventType = searchParams.get('event_type')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    // Determine which user's logs to fetch
    let targetUserId = user.id

    // Check if requesting another user's logs (admin only)
    if (requestedUserId && requestedUserId !== user.id) {
      // Check if current user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('email')
        .eq('id', user.id)
        .single()

      const isAdmin = profile?.email === process.env.ADMIN_EMAIL
      
      if (!isAdmin) {
        return NextResponse.json(
          { error: 'Forbidden: Admin access required' },
          { status: 403 }
        )
      }
      
      targetUserId = requestedUserId
    }

    // Build query
    let query = supabase
      .from('logs')
      .select(`
        id,
        user_id,
        automation_type,
        event_type,
        message,
        details,
        workflow_execution_id,
        timestamp,
        profiles!logs_user_id_fkey(
          full_name,
          business_name,
          email
        )
      `)
      .eq('user_id', targetUserId)
      .order('timestamp', { ascending: false })

    // Apply filters
    if (automationType) {
      query = query.eq('automation_type', automationType)
    }

    if (eventType) {
      query = query.eq('event_type', eventType)
    }

    if (startDate) {
      query = query.gte('timestamp', startDate)
    }

    if (endDate) {
      query = query.lte('timestamp', endDate)
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data: logs, error: logsError } = await query

    if (logsError) {
      console.error('Database error fetching logs:', logsError)
      return NextResponse.json(
        { error: 'Failed to fetch logs' },
        { status: 500 }
      )
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('logs')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', targetUserId)

    if (automationType) {
      countQuery = countQuery.eq('automation_type', automationType)
    }
    if (eventType) {
      countQuery = countQuery.eq('event_type', eventType)
    }
    if (startDate) {
      countQuery = countQuery.gte('timestamp', startDate)
    }
    if (endDate) {
      countQuery = countQuery.lte('timestamp', endDate)
    }

    const { count, error: countError } = await countQuery

    if (countError) {
      console.error('Database error counting logs:', countError)
    }

    // Calculate summary statistics
    const summaryQuery = supabase
      .from('logs')
      .select('event_type')
      .eq('user_id', targetUserId)

    if (startDate) {
      summaryQuery.gte('timestamp', startDate)
    }
    if (endDate) {
      summaryQuery.lte('timestamp', endDate)
    }

    const { data: summaryData } = await summaryQuery

    const summary = {
      total: count || 0,
      success: summaryData?.filter(log => log.event_type === 'success').length || 0,
      error: summaryData?.filter(log => log.event_type === 'error').length || 0,
      warning: summaryData?.filter(log => log.event_type === 'warning').length || 0,
      info: summaryData?.filter(log => log.event_type === 'info').length || 0
    }

    return NextResponse.json({
      logs,
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      },
      summary,
      filters: {
        user_id: targetUserId,
        automation_type: automationType,
        event_type: eventType,
        start_date: startDate,
        end_date: endDate
      }
    })

  } catch (error) {
    console.error('Logs API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/logs - Create a new log entry (for internal use)
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      automation_type,
      event_type,
      message,
      details,
      workflow_execution_id
    } = body

    // Validate required fields
    if (!automation_type || !event_type || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: automation_type, event_type, message' },
        { status: 400 }
      )
    }

    // Validate event_type
    const validEventTypes = ['success', 'error', 'warning', 'info']
    if (!validEventTypes.includes(event_type)) {
      return NextResponse.json(
        { error: 'Invalid event_type. Must be one of: success, error, warning, info' },
        { status: 400 }
      )
    }

    // Validate automation_type
    const validAutomationTypes = ['review_booster', 'seo_responder', 'social_poster']
    if (!validAutomationTypes.includes(automation_type)) {
      return NextResponse.json(
        { error: 'Invalid automation_type' },
        { status: 400 }
      )
    }

    // Insert log entry
    const { data: logEntry, error: insertError } = await supabase
      .from('logs')
      .insert({
        user_id: user.id,
        automation_type,
        event_type,
        message,
        details: details || null,
        workflow_execution_id: workflow_execution_id || null,
        timestamp: new Date().toISOString()
      })
      .select()
      .single()

    if (insertError) {
      console.error('Database error inserting log:', insertError)
      return NextResponse.json(
        { error: 'Failed to create log entry' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      log: logEntry
    })

  } catch (error) {
    console.error('Logs POST API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/logs - Clear logs (admin only)
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin access
    const { data: profile } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', user.id)
      .single()

    const isAdmin = profile?.email === process.env.ADMIN_EMAIL
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const olderThan = searchParams.get('older_than') // ISO date string
    const automationType = searchParams.get('automation_type')

    let deleteQuery = supabase.from('logs').delete()

    if (userId) {
      deleteQuery = deleteQuery.eq('user_id', userId)
    }

    if (olderThan) {
      deleteQuery = deleteQuery.lt('timestamp', olderThan)
    }

    if (automationType) {
      deleteQuery = deleteQuery.eq('automation_type', automationType)
    }

    const { error: deleteError } = await deleteQuery

    if (deleteError) {
      console.error('Database error deleting logs:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete logs' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Logs deleted successfully'
    })

  } catch (error) {
    console.error('Logs DELETE API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}