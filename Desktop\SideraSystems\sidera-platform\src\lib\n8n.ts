import axios from 'axios'

const N8N_API_URL = process.env.N8N_API_URL!
const N8N_API_KEY = process.env.N8N_API_KEY!

const n8nAPI = axios.create({
  baseURL: N8N_API_URL,
  headers: {
    'X-N8N-API-KEY': N8N_API_KEY,
    'Content-Type': 'application/json',
  },
})

export interface Workflow {
  id: string
  name: string
  active: boolean
  nodes: any[]
  connections: any
  settings: any
  staticData?: any
  createdAt: string
  updatedAt: string
}

export interface WorkflowExecution {
  id: string
  workflowId: string
  mode: string
  startedAt: string
  stoppedAt?: string
  status: 'new' | 'running' | 'success' | 'error' | 'waiting'
  data?: any
}

// Workflow Templates
export const WORKFLOW_TEMPLATES = {
  review_booster: {
    name: 'Review Booster',
    nodes: [
      {
        id: 'schedule-trigger',
        type: 'n8n-nodes-base.scheduleTrigger',
        name: 'Schedule Trigger',
        parameters: {
          rule: {
            interval: [
              {
                field: 'cronExpression',
                value: '0 9 * * *', // Default: 9 AM daily
              },
            ],
          },
        },
        position: [250, 300],
      },
      {
        id: 'get-user-config',
        type: 'n8n-nodes-base.supabase',
        name: 'Get User Config',
        parameters: {
          operation: 'getAll',
          table: 'client_configs',
          filterType: 'manual',
          conditions: {
            conditions: [
              {
                keyName: 'user_id',
                operation: 'equals',
                value: '={{$workflow.settings.userId}}',
              },
            ],
          },
        },
        position: [450, 300],
      },
      {
        id: 'send-review-request',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Send Review Request',
        parameters: {
          url: 'https://api.twilio.com/2010-04-01/Accounts/{{$workflow.settings.twilioAccountSid}}/Messages.json',
          method: 'POST',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'twilioApi',
          sendBody: true,
          bodyContentType: 'form-urlencoded',
          bodyParameters: {
            parameters: [
              {
                name: 'To',
                value: '={{$workflow.settings.customerPhone}}',
              },
              {
                name: 'From',
                value: '={{$workflow.settings.businessPhone}}',
              },
              {
                name: 'Body',
                value: '={{$workflow.settings.reviewRequestMessage}}',
              },
            ],
          },
        },
        position: [650, 300],
      },
      {
        id: 'log-result',
        type: 'n8n-nodes-base.supabase',
        name: 'Log Result',
        parameters: {
          operation: 'insert',
          table: 'logs',
          rows: {
            values: {
              user_id: '={{$workflow.settings.userId}}',
              automation_type: 'review_booster',
              event_type: 'success',
              message: 'Review request sent successfully',
              details: '={{JSON.stringify($json)}}',
              workflow_execution_id: '={{$workflow.id}}',
            },
          },
        },
        position: [850, 300],
      },
    ],
    connections: {
      'Schedule Trigger': {
        main: [
          [
            {
              node: 'Get User Config',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Get User Config': {
        main: [
          [
            {
              node: 'Send Review Request',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Send Review Request': {
        main: [
          [
            {
              node: 'Log Result',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
    },
  },
  seo_responder: {
    name: 'SEO Responder',
    nodes: [
      {
        id: 'schedule-trigger',
        type: 'n8n-nodes-base.scheduleTrigger',
        name: 'Schedule Trigger',
        parameters: {
          rule: {
            interval: [
              {
                field: 'cronExpression',
                value: '0 */4 * * *', // Default: Every 4 hours
              },
            ],
          },
        },
        position: [250, 300],
      },
      {
        id: 'get-user-config',
        type: 'n8n-nodes-base.supabase',
        name: 'Get User Config',
        parameters: {
          operation: 'getAll',
          table: 'client_configs',
          filterType: 'manual',
          conditions: {
            conditions: [
              {
                keyName: 'user_id',
                operation: 'equals',
                value: '={{$workflow.settings.userId}}',
              },
            ],
          },
        },
        position: [450, 300],
      },
      {
        id: 'fetch-google-reviews',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Fetch Google Reviews',
        parameters: {
          url: 'https://mybusinessbusinessinformation.googleapis.com/v1/accounts/{{$workflow.settings.googleAccountId}}/locations/{{$workflow.settings.locationId}}/reviews',
          method: 'GET',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'googleApi',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Authorization',
                value: 'Bearer {{$workflow.settings.googleApiKey}}',
              },
            ],
          },
        },
        position: [650, 300],
      },
      {
        id: 'filter-new-reviews',
        type: 'n8n-nodes-base.if',
        name: 'Filter New Reviews',
        parameters: {
          conditions: {
            boolean: [
              {
                value1: '={{$json.createTime}}',
                operation: 'isAfterDate',
                value2: '={{DateTime.now().minus({ hours: 4 }).toISO()}}',
              },
            ],
          },
        },
        position: [850, 300],
      },
      {
        id: 'generate-ai-response',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Generate AI Response',
        parameters: {
          url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
          method: 'POST',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'googleApi',
          sendBody: true,
          bodyContentType: 'json',
          jsonParameters: true,
          bodyParametersJson: '={\n  "contents": [{\n    "parts": [{\n      "text": "Generate a professional, SEO-optimized response to this Google Business review. Keep it under 150 characters, sound genuine, and include relevant keywords for {{$workflow.settings.businessCategory}}. Review: \\"{{$json.comment}}\\". Rating: {{$json.starRating}} stars. Business: {{$workflow.settings.businessName}}"\n    }]\n  }],\n  "generationConfig": {\n    "temperature": 0.7,\n    "maxOutputTokens": 100\n  }\n}',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Authorization',
                value: 'Bearer {{$workflow.settings.geminiApiKey}}',
              },
              {
                name: 'Content-Type',
                value: 'application/json',
              },
            ],
          },
        },
        position: [1050, 250],
      },
      {
        id: 'post-review-response',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Post Review Response',
        parameters: {
          url: 'https://mybusinessbusinessinformation.googleapis.com/v1/accounts/{{$workflow.settings.googleAccountId}}/locations/{{$workflow.settings.locationId}}/reviews/{{$json.reviewId}}/reply',
          method: 'PUT',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'googleApi',
          sendBody: true,
          bodyContentType: 'json',
          jsonParameters: true,
          bodyParametersJson: '={\n  "comment": "{{$node[\\"Generate AI Response\\"].json.candidates[0].content.parts[0].text}}"\n}',
        },
        position: [1250, 250],
      },
      {
        id: 'log-success',
        type: 'n8n-nodes-base.supabase',
        name: 'Log Success',
        parameters: {
          operation: 'insert',
          table: 'logs',
          rows: {
            values: {
              user_id: '={{$workflow.settings.userId}}',
              automation_type: 'seo_responder',
              event_type: 'success',
              message: 'AI response posted to review',
              details: '={{JSON.stringify({reviewId: $json.reviewId, response: $node["Generate AI Response"].json.candidates[0].content.parts[0].text})}}',
              workflow_execution_id: '={{$workflow.id}}',
            },
          },
        },
        position: [1450, 250],
      },
      {
        id: 'log-no-new-reviews',
        type: 'n8n-nodes-base.supabase',
        name: 'Log No New Reviews',
        parameters: {
          operation: 'insert',
          table: 'logs',
          rows: {
            values: {
              user_id: '={{$workflow.settings.userId}}',
              automation_type: 'seo_responder',
              event_type: 'info',
              message: 'No new reviews to respond to',
              details: '={{JSON.stringify({checkTime: DateTime.now().toISO()})}}',
              workflow_execution_id: '={{$workflow.id}}',
            },
          },
        },
        position: [1050, 350],
      },
    ],
    connections: {
      'Schedule Trigger': {
        main: [
          [
            {
              node: 'Get User Config',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Get User Config': {
        main: [
          [
            {
              node: 'Fetch Google Reviews',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Fetch Google Reviews': {
        main: [
          [
            {
              node: 'Filter New Reviews',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Filter New Reviews': {
        main: [
          [
            {
              node: 'Generate AI Response',
              type: 'main',
              index: 0,
            },
          ],
          [
            {
              node: 'Log No New Reviews',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Generate AI Response': {
        main: [
          [
            {
              node: 'Post Review Response',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Post Review Response': {
        main: [
          [
            {
              node: 'Log Success',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
    },
  },
  social_poster: {
    name: 'Social Poster',
    nodes: [
      {
        id: 'schedule-trigger',
        type: 'n8n-nodes-base.scheduleTrigger',
        name: 'Schedule Trigger',
        parameters: {
          rule: {
            interval: [
              {
                field: 'cronExpression',
                value: '0 10 * * 1', // Default: Weekly on Monday at 10 AM
              },
            ],
          },
        },
        position: [250, 300],
      },
      {
        id: 'get-user-config',
        type: 'n8n-nodes-base.supabase',
        name: 'Get User Config',
        parameters: {
          operation: 'getAll',
          table: 'client_configs',
          filterType: 'manual',
          conditions: {
            conditions: [
              {
                keyName: 'user_id',
                operation: 'equals',
                value: '={{$workflow.settings.userId}}',
              },
            ],
          },
        },
        position: [450, 300],
      },
      {
        id: 'generate-content-idea',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Generate Content Idea',
        parameters: {
          url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
          method: 'POST',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'googleApi',
          sendBody: true,
          bodyContentType: 'json',
          jsonParameters: true,
          bodyParametersJson: '={\n  "contents": [{\n    "parts": [{\n      "text": "Create an educational social media post for a {{$workflow.settings.businessCategory}} business called {{$workflow.settings.businessName}}. The post should be helpful, engaging, and under 280 characters. Include relevant tips or insights for customers. Add 2-3 relevant hashtags. Make it sound professional but approachable."\n    }]\n  }],\n  "generationConfig": {\n    "temperature": 0.8,\n    "maxOutputTokens": 150\n  }\n}',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Authorization',
                value: 'Bearer {{$workflow.settings.geminiApiKey}}',
              },
              {
                name: 'Content-Type',
                value: 'application/json',
              },
            ],
          },
        },
        position: [650, 300],
      },
      {
        id: 'post-to-facebook',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Post to Facebook',
        parameters: {
          url: 'https://graph.facebook.com/v18.0/{{$workflow.settings.facebookPageId}}/feed',
          method: 'POST',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'facebookGraphApi',
          sendBody: true,
          bodyContentType: 'form-urlencoded',
          bodyParameters: {
            parameters: [
              {
                name: 'message',
                value: '={{$node["Generate Content Idea"].json.candidates[0].content.parts[0].text}}',
              },
              {
                name: 'access_token',
                value: '={{$workflow.settings.facebookAccessToken}}',
              },
            ],
          },
        },
        position: [850, 300],
      },
      {
        id: 'post-to-google-business',
        type: 'n8n-nodes-base.httpRequest',
        name: 'Post to Google Business',
        parameters: {
          url: 'https://mybusinessbusinessinformation.googleapis.com/v1/accounts/{{$workflow.settings.googleAccountId}}/locations/{{$workflow.settings.locationId}}/localPosts',
          method: 'POST',
          authentication: 'predefinedCredentialType',
          nodeCredentialType: 'googleApi',
          sendBody: true,
          bodyContentType: 'json',
          jsonParameters: true,
          bodyParametersJson: '={\n  "languageCode": "en-US",\n  "summary": "{{$node[\\"Generate Content Idea\\"].json.candidates[0].content.parts[0].text}}",\n  "event": {\n    "title": "Helpful Tips from {{$workflow.settings.businessName}}"\n  },\n  "callToAction": {\n    "actionType": "LEARN_MORE",\n    "url": "{{$workflow.settings.businessWebsite}}"\n  }\n}',
          sendHeaders: true,
          headerParameters: {
            parameters: [
              {
                name: 'Authorization',
                value: 'Bearer {{$workflow.settings.googleApiKey}}',
              },
              {
                name: 'Content-Type',
                value: 'application/json',
              },
            ],
          },
        },
        position: [850, 400],
      },
      {
        id: 'log-facebook-success',
        type: 'n8n-nodes-base.supabase',
        name: 'Log Facebook Success',
        parameters: {
          operation: 'insert',
          table: 'logs',
          rows: {
            values: {
              user_id: '={{$workflow.settings.userId}}',
              automation_type: 'social_poster',
              event_type: 'success',
              message: 'Educational post published to Facebook',
              details: '={{JSON.stringify({platform: "facebook", postId: $json.id, content: $node["Generate Content Idea"].json.candidates[0].content.parts[0].text})}}',
              workflow_execution_id: '={{$workflow.id}}',
            },
          },
        },
        position: [1050, 300],
      },
      {
        id: 'log-google-success',
        type: 'n8n-nodes-base.supabase',
        name: 'Log Google Success',
        parameters: {
          operation: 'insert',
          table: 'logs',
          rows: {
            values: {
              user_id: '={{$workflow.settings.userId}}',
              automation_type: 'social_poster',
              event_type: 'success',
              message: 'Educational post published to Google Business',
              details: '={{JSON.stringify({platform: "google_business", postName: $json.name, content: $node["Generate Content Idea"].json.candidates[0].content.parts[0].text})}}',
              workflow_execution_id: '={{$workflow.id}}',
            },
          },
        },
        position: [1050, 400],
      },
    ],
    connections: {
      'Schedule Trigger': {
        main: [
          [
            {
              node: 'Get User Config',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Get User Config': {
        main: [
          [
            {
              node: 'Generate Content Idea',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Generate Content Idea': {
        main: [
          [
            {
              node: 'Post to Facebook',
              type: 'main',
              index: 0,
            },
            {
              node: 'Post to Google Business',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Post to Facebook': {
        main: [
          [
            {
              node: 'Log Facebook Success',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
      'Post to Google Business': {
        main: [
          [
            {
              node: 'Log Google Success',
              type: 'main',
              index: 0,
            },
          ],
        ],
      },
    },
  },
}

export class N8NService {
  // Get all workflows
  static async getWorkflows(): Promise<Workflow[]> {
    try {
      const response = await n8nAPI.get('/workflows')
      return response.data.data
    } catch (error) {
      console.error('Error fetching workflows:', error)
      throw error
    }
  }

  // Get specific workflow
  static async getWorkflow(workflowId: string): Promise<Workflow> {
    try {
      const response = await n8nAPI.get(`/workflows/${workflowId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching workflow:', error)
      throw error
    }
  }

  // Create new workflow
  static async createWorkflow(workflowData: any): Promise<Workflow> {
    try {
      const response = await n8nAPI.post('/workflows', workflowData)
      return response.data
    } catch (error) {
      console.error('Error creating workflow:', error)
      throw error
    }
  }

  // Update workflow
  static async updateWorkflow(workflowId: string, workflowData: any): Promise<Workflow> {
    try {
      const response = await n8nAPI.put(`/workflows/${workflowId}`, workflowData)
      return response.data
    } catch (error) {
      console.error('Error updating workflow:', error)
      throw error
    }
  }

  // Activate/Deactivate workflow
  static async setWorkflowActive(workflowId: string, active: boolean): Promise<void> {
    try {
      await n8nAPI.post(`/workflows/${workflowId}/${active ? 'activate' : 'deactivate'}`)
    } catch (error) {
      console.error('Error setting workflow active state:', error)
      throw error
    }
  }

  // Execute workflow manually
  static async executeWorkflow(workflowId: string, data?: any): Promise<WorkflowExecution> {
    try {
      const response = await n8nAPI.post(`/workflows/${workflowId}/execute`, data)
      return response.data
    } catch (error) {
      console.error('Error executing workflow:', error)
      throw error
    }
  }

  // Get workflow executions
  static async getWorkflowExecutions(workflowId: string): Promise<WorkflowExecution[]> {
    try {
      const response = await n8nAPI.get(`/executions?workflowId=${workflowId}`)
      return response.data.data
    } catch (error) {
      console.error('Error fetching workflow executions:', error)
      throw error
    }
  }

  // Update workflow schedule (cron)
  static async updateWorkflowSchedule(workflowId: string, cronExpression: string): Promise<void> {
    try {
      const workflow = await this.getWorkflow(workflowId)
      
      // Find the schedule trigger node and update its cron expression
      const scheduleNode = workflow.nodes.find(
        (node) => node.type === 'n8n-nodes-base.scheduleTrigger'
      )
      
      if (scheduleNode) {
        scheduleNode.parameters.rule.interval[0].value = cronExpression
        await this.updateWorkflow(workflowId, workflow)
      }
    } catch (error) {
      console.error('Error updating workflow schedule:', error)
      throw error
    }
  }

  // Create user-specific workflow from template
  static async createUserWorkflow(
    userId: string,
    automationType: 'review_booster' | 'seo_responder' | 'social_poster',
    cronExpression: string,
    userSettings: any
  ): Promise<Workflow> {
    try {
      const template = WORKFLOW_TEMPLATES[automationType]
      
      const workflowData = {
        ...template,
        name: `${template.name} - ${userId}`,
        settings: {
          userId,
          ...userSettings,
        },
      }

      // Update cron expression in schedule trigger
      const scheduleNode = workflowData.nodes.find(
        (node) => node.type === 'n8n-nodes-base.scheduleTrigger'
      )
      if (scheduleNode) {
        scheduleNode.parameters.rule.interval[0].value = cronExpression
      }

      const workflow = await this.createWorkflow(workflowData)
      return workflow
    } catch (error) {
      console.error('Error creating user workflow:', error)
      throw error
    }
  }
}

// Export simplified API that matches what other files expect
export const n8nApi = {
  // Get workflow
  getWorkflow: (workflowId: string) => N8NService.getWorkflow(workflowId),
  
  // Create workflow
  createWorkflow: (workflowData: any) => N8NService.createWorkflow(workflowData),
  
  // Update workflow
  updateWorkflow: (workflowId: string, workflowData: any) => N8NService.updateWorkflow(workflowId, workflowData),
  
  // Activate workflow
  activateWorkflow: (workflowId: string) => N8NService.setWorkflowActive(workflowId, true),
  
  // Deactivate workflow
  deactivateWorkflow: (workflowId: string) => N8NService.setWorkflowActive(workflowId, false),
  
  // Execute workflow
  executeWorkflow: (workflowId: string, data?: any) => N8NService.executeWorkflow(workflowId, data),
  
  // Get executions
  getWorkflowExecutions: (workflowId: string) => N8NService.getWorkflowExecutions(workflowId),
  
  // Update schedule
  updateWorkflowSchedule: (workflowId: string, cronExpression: string) => N8NService.updateWorkflowSchedule(workflowId, cronExpression),
  
  // Update workflow settings
  updateWorkflowSettings: async (workflowId: string, settings: any) => {
    try {
      const workflow = await N8NService.getWorkflow(workflowId)
      workflow.settings = { ...workflow.settings, ...settings }
      return await N8NService.updateWorkflow(workflowId, workflow)
    } catch (error) {
      console.error('Error updating workflow settings:', error)
      throw error
    }
  },
  
  // Create user-specific workflow
  createUserWorkflow: (
    userId: string,
    automationType: 'review_booster' | 'seo_responder' | 'social_poster',
    cronExpression: string,
    userSettings: any
  ) => N8NService.createUserWorkflow(userId, automationType, cronExpression, userSettings)
}

export default N8NService