export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          business_name: string | null
          business_category: string | null
          business_location: string | null
          subscription_tier: 'basic' | 'standard' | 'premium' | null
          subscription_status: 'active' | 'cancelled' | 'past_due' | null
          stripe_customer_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          business_name?: string | null
          business_category?: string | null
          business_location?: string | null
          subscription_tier?: 'basic' | 'standard' | 'premium' | null
          subscription_status?: 'active' | 'cancelled' | 'past_due' | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          business_name?: string | null
          business_category?: string | null
          business_location?: string | null
          subscription_tier?: 'basic' | 'standard' | 'premium' | null
          subscription_status?: 'active' | 'cancelled' | 'past_due' | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      client_configs: {
        Row: {
          id: string
          user_id: string
          google_business_api_key: string | null
          google_business_location_id: string | null
          preferred_tone: 'friendly' | 'professional' | 'casual' | null
          contact_phone: string | null
          contact_email: string | null
          business_hours: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          google_business_api_key?: string | null
          google_business_location_id?: string | null
          preferred_tone?: 'friendly' | 'professional' | 'casual' | null
          contact_phone?: string | null
          contact_email?: string | null
          business_hours?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          google_business_api_key?: string | null
          google_business_location_id?: string | null
          preferred_tone?: 'friendly' | 'professional' | 'casual' | null
          contact_phone?: string | null
          contact_email?: string | null
          business_hours?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "client_configs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      workflow_auth_configs: {
        Row: {
          id: string
          user_id: string
          workflow_type: 'review_booster' | 'seo_responder' | 'social_poster'
          provider: string
          auth_method: 'oauth' | 'api_key' | 'webhook' | 'email_password'
          auth_data: Json
          status: 'connected' | 'expired' | 'error' | 'pending'
          expires_at: string | null
          provider_user_id: string | null
          provider_username: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          workflow_type: 'review_booster' | 'seo_responder' | 'social_poster'
          provider: string
          auth_method: 'oauth' | 'api_key' | 'webhook' | 'email_password'
          auth_data: Json
          status?: 'connected' | 'expired' | 'error' | 'pending'
          expires_at?: string | null
          provider_user_id?: string | null
          provider_username?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          workflow_type?: 'review_booster' | 'seo_responder' | 'social_poster'
          provider?: string
          auth_method?: 'oauth' | 'api_key' | 'webhook' | 'email_password'
          auth_data?: Json
          status?: 'connected' | 'expired' | 'error' | 'pending'
          expires_at?: string | null
          provider_user_id?: string | null
          provider_username?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "workflow_auth_configs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      automation_configs: {
        Row: {
          id: string
          user_id: string
          automation_type: 'review_booster' | 'seo_responder' | 'social_poster'
          is_active: boolean
          cron_schedule: string
          workflow_id: string | null
          settings: Json | null
          last_run: string | null
          next_run: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          automation_type: 'review_booster' | 'seo_responder' | 'social_poster'
          is_active?: boolean
          cron_schedule: string
          workflow_id?: string | null
          settings?: Json | null
          last_run?: string | null
          next_run?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          automation_type?: 'review_booster' | 'seo_responder' | 'social_poster'
          is_active?: boolean
          cron_schedule?: string
          workflow_id?: string | null
          settings?: Json | null
          last_run?: string | null
          next_run?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "automation_configs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      logs: {
        Row: {
          id: string
          user_id: string
          automation_type: 'review_booster' | 'seo_responder' | 'social_poster'
          event_type: 'success' | 'error' | 'warning' | 'info'
          message: string
          details: Json | null
          workflow_execution_id: string | null
          timestamp: string
        }
        Insert: {
          id?: string
          user_id: string
          automation_type: 'review_booster' | 'seo_responder' | 'social_poster'
          event_type: 'success' | 'error' | 'warning' | 'info'
          message: string
          details?: Json | null
          workflow_execution_id?: string | null
          timestamp?: string
        }
        Update: {
          id?: string
          user_id?: string
          automation_type?: 'review_booster' | 'seo_responder' | 'social_poster'
          event_type?: 'success' | 'error' | 'warning' | 'info'
          message?: string
          details?: Json | null
          workflow_execution_id?: string | null
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      posts: {
        Row: {
          id: string
          title: string
          slug: string
          content: string
          excerpt: string | null
          category: string | null
          tags: string[] | null
          seo_keywords: string[] | null
          published: boolean
          featured_image: string | null
          author: string | null
          published_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          slug: string
          content: string
          excerpt?: string | null
          category?: string | null
          tags?: string[] | null
          seo_keywords?: string[] | null
          published?: boolean
          featured_image?: string | null
          author?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          content?: string
          excerpt?: string | null
          category?: string | null
          tags?: string[] | null
          seo_keywords?: string[] | null
          published?: boolean
          featured_image?: string | null
          author?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}